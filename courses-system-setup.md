# InnoHub Courses System Setup

## Overview
The courses system is integrated into the main InnoHub website at `localhost:3001/courses` with a dedicated courses section.

## Setup Instructions

### 1. Start the development server
```bash
npm run dev
```

### 2. Access the application

**Main Website:**
- URL: `http://localhost:3001`
- Features: Home, About, Programs, Investors, News, Courses
- Navbar: Includes "Courses" button (replaces Contact Us) that links to `/courses`

**Courses Section:**
- URL: `http://localhost:3001/courses`
- Features: Course system, authentication, onboarding
- Uses the same navbar as the main site for consistency

## How it works

1. **Main Website (`localhost:3001`):**
   - Serves the complete InnoHub website including courses
   - "Courses" button in navbar (replaces Contact Us) links to `/courses`
   - Unified navigation and consistent user experience

2. **Courses Section (`localhost:3001/courses`):**
   - Integrated courses application within the main site
   - Same navbar as main site for consistency
   - Authentication system
   - Onboarding and assessment system
   - Course catalog and learning platform

## Navigation Flow

1. Users visit main site at `localhost:3001`
2. Click "Courses" button in navbar → navigates to `/courses`
3. Courses welcome page introduces the system
4. Authentication flow leads to onboarding
5. After onboarding, users access personalized course dashboard
6. All navigation stays within the same domain

## Technical Implementation

- **Middleware**: Handles course route protection and authentication
- **Unified Navbar**: Same navigation across all pages
- **Protected Routes**: Authentication required for course access
- **Shared Components**: UI components work consistently
- **Bilingual Support**: Full Mongolian/English support

## Testing

1. Visit `http://localhost:3001` - main site with courses button
2. Click "Courses" button - should navigate to `/courses`
3. Try authentication flow within the same domain
4. Test onboarding and course access
5. Verify all functionality works on single domain
