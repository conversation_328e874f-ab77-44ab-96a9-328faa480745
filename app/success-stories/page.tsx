'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { companies } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { Breadcrumb, breadcrumbConfigs } from '@/components/ui/breadcrumb';
import { Badge } from '@/components/ui/badge';
import { AuthButton } from '@/components/ui/auth-button';
import { Building2, Users, Target, Calendar, ExternalLink, BookOpen, TrendingUp, ArrowRight } from 'lucide-react';

export default function SuccessStoriesPage() {
  return (
    <AnimatedGradientBackground className="min-h-screen">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Breadcrumb */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Breadcrumb items={breadcrumbConfigs.successStories()} />
        </motion.div>

        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="inline-block rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Success Stories
          </motion.div>
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Transforming Ideas into{' '}
            <span className="bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
              Success
            </span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Discover the innovative startups that have thrived with our comprehensive incubator support, 
            achieving remarkable growth and making real impact in their industries.
          </p>
        </motion.div>

        {/* Stats Overview */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <AuthCard>
            <AuthCardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">{companies.length}</div>
              <div className="text-sm text-muted-foreground">Portfolio Companies</div>
            </AuthCardContent>
          </AuthCard>
          
          <AuthCard>
            <AuthCardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">
                {Math.round(companies.reduce((acc, c) => acc + c.progress.overall, 0) / companies.length)}%
              </div>
              <div className="text-sm text-muted-foreground">Avg Progress</div>
            </AuthCardContent>
          </AuthCard>
          
          <AuthCard>
            <AuthCardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">
                {companies.reduce((acc, c) => acc + c.mentors.length, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Total Mentors</div>
            </AuthCardContent>
          </AuthCard>
          
          <AuthCard>
            <AuthCardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">
                {new Set(companies.map(c => c.industry)).size}
              </div>
              <div className="text-sm text-muted-foreground">Industries</div>
            </AuthCardContent>
          </AuthCard>
        </motion.div>

        {/* Companies Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {companies.map((company, index) => (
            <motion.div
              key={company.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <AuthCard className="h-full group hover:border-primary/40 transition-colors duration-300">
                <AuthCardContent className="p-0">
                  {/* Company Header */}
                  <div className="relative p-6 pb-4">
                    <div className="flex items-start gap-4 mb-4">
                      <div className="w-16 h-16 rounded-xl bg-white/10 border border-primary/20 flex items-center justify-center overflow-hidden">
                        {company.logo ? (
                          <Image
                            src={company.logo}
                            alt={`${company.name} logo`}
                            width={48}
                            height={48}
                            className="object-contain"
                          />
                        ) : (
                          <Building2 className="h-8 w-8 text-primary" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-white mb-1 group-hover:text-primary transition-colors duration-300">
                          {company.name}
                        </h3>
                        <p className="text-sm text-primary font-medium">{company.industry}</p>
                        <Badge variant="secondary" className="mt-1 text-xs bg-green-500/20 text-green-400 border-green-500/30">
                          {company.currentStage}
                        </Badge>
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                      {company.description}
                    </p>

                    {/* Program Track */}
                    <div className="flex items-center gap-2 mb-4">
                      <span className="text-lg">{company.programTrack.icon}</span>
                      <span className="text-sm text-muted-foreground">{company.programTrack.name}</span>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="px-6 mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-white">Progress</span>
                      <span className="text-sm text-muted-foreground">{company.progress.overall}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-primary to-purple-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: `${company.progress.overall}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                        viewport={{ once: true }}
                      />
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="px-6 py-4 border-t border-primary/20">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="flex items-center justify-center mb-1">
                          <Users className="h-3 w-3 text-blue-400 mr-1" />
                          <span className="text-xs text-white/60">Mentors</span>
                        </div>
                        <div className="text-sm font-semibold text-blue-400">{company.mentors.length}</div>
                      </div>
                      <div>
                        <div className="flex items-center justify-center mb-1">
                          <Target className="h-3 w-3 text-purple-400 mr-1" />
                          <span className="text-xs text-white/60">Milestones</span>
                        </div>
                        <div className="text-sm font-semibold text-purple-400">{company.milestones.length}</div>
                      </div>
                      <div>
                        <div className="flex items-center justify-center mb-1">
                          <Calendar className="h-3 w-3 text-green-400 mr-1" />
                          <span className="text-xs text-white/60">Joined</span>
                        </div>
                        <div className="text-sm font-semibold text-green-400">
                          {new Date(company.joinDate).getFullYear()}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="p-6 pt-4 space-y-2">
                    <Link href={`/companies/${company.id}/instructions`} className="block">
                      <AuthButton variant="primary" className="w-full justify-center">
                        <BookOpen className="h-4 w-4 mr-2" />
                        View Company Guide
                      </AuthButton>
                    </Link>
                    
                    {company.website && (
                      <AuthButton
                        variant="outline"
                        className="w-full justify-center"
                        onClick={() => window.open(company.website, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Visit Website
                      </AuthButton>
                    )}
                  </div>
                </AuthCardContent>
              </AuthCard>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <AuthCard>
            <AuthCardHeader>
              <AuthCardTitle>Ready to Join Our Success Stories?</AuthCardTitle>
            </AuthCardHeader>
            <AuthCardContent>
              <p className="text-muted-foreground mb-6">
                Take the first step towards transforming your innovative idea into a thriving business. 
                Apply to our incubation programs and get the support you need to succeed.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/programs">
                  <AuthButton variant="primary" className="group">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Apply to Programs
                    <ArrowRight className="h-4 w-4 ml-2 transition-transform group-hover:translate-x-1" />
                  </AuthButton>
                </Link>
                <Link href="/programs/portfolio">
                  <AuthButton variant="outline" className="group">
                    <BookOpen className="h-4 w-4 mr-2" />
                    View Portfolio
                    <ArrowRight className="h-4 w-4 ml-2 transition-transform group-hover:translate-x-1" />
                  </AuthButton>
                </Link>
                <Link href="/contact">
                  <AuthButton variant="outline">
                    Get in Touch
                  </AuthButton>
                </Link>
              </div>
            </AuthCardContent>
          </AuthCard>
        </motion.div>
      </div>
    </AnimatedGradientBackground>
  );
}
