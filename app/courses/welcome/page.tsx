'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { 
  BookOpen, 
  Users, 
  Award, 
  TrendingUp, 
  Play, 
  ArrowRight,
  Sparkles,
  Target,
  Brain,
  Lightbulb
} from 'lucide-react';

const features = [
  {
    icon: <Brain className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature1.title',
    descKey: 'courses.welcome.feature1.desc',
    color: 'text-blue-400'
  },
  {
    icon: <Target className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature2.title', 
    descKey: 'courses.welcome.feature2.desc',
    color: 'text-green-400'
  },
  {
    icon: <Users className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature3.title',
    descKey: 'courses.welcome.feature3.desc',
    color: 'text-purple-400'
  },
  {
    icon: <Award className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature4.title',
    descKey: 'courses.welcome.feature4.desc',
    color: 'text-orange-400'
  }
];

export default function CoursesWelcomePage() {
  const { t, isLoaded } = useLanguage();

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
          <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
          <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <AnimatedGradientBackground className="min-h-screen py-20">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-6">
            <div className="rounded-full bg-primary/10 p-4">
              <BookOpen className="h-16 w-16 text-primary" />
            </div>
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-purple-200 to-purple-400 bg-clip-text text-transparent">
            {t('courses.welcome.title') || 'Welcome to InnoHub Courses'}
          </h1>
          
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            {t('courses.welcome.subtitle') || 'Discover your talents, unlock your potential, and accelerate your entrepreneurial journey with personalized learning experiences.'}
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/login">
              <Button size="lg" className="bg-primary hover:bg-primary/90 flex items-center gap-2">
                <Play className="h-5 w-5" />
                {t('courses.welcome.getStarted') || 'Get Started'}
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
            
            <Link href="/about">
              <Button variant="outline" size="lg" className="border-primary/20 hover:bg-primary/10">
                {t('courses.welcome.learnMore') || 'Learn More'}
              </Button>
            </Link>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1 }}
            >
              <Card className="border-primary/20 bg-black/60 backdrop-blur-md h-full">
                <CardHeader className="text-center">
                  <div className={`${feature.color} flex justify-center mb-4`}>
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">
                    {t(feature.titleKey) || 'Feature Title'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    {t(feature.descKey) || 'Feature description'}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Process Steps */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          className="mb-16"
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              {t('courses.welcome.howItWorks') || 'How It Works'}
            </h2>
            <p className="text-muted-foreground text-lg">
              {t('courses.welcome.processDesc') || 'Your personalized learning journey in three simple steps'}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[1, 2, 3].map((step, index) => (
              <motion.div
                key={step}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 + index * 0.2 }}
                className="text-center"
              >
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-2xl font-bold text-primary">{step}</span>
                  </div>
                  {index < 2 && (
                    <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-primary/20 -translate-y-1/2"></div>
                  )}
                </div>
                <h3 className="text-xl font-semibold mb-3">
                  {t(`courses.welcome.step${step}.title`) || `Step ${step}`}
                </h3>
                <p className="text-muted-foreground">
                  {t(`courses.welcome.step${step}.desc`) || 'Step description'}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.6 }}
        >
          <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
            <CardContent className="text-center py-12">
              <div className="flex items-center justify-center mb-6">
                <div className="rounded-full bg-primary/10 p-3">
                  <Sparkles className="h-8 w-8 text-primary" />
                </div>
              </div>
              
              <h2 className="text-3xl font-bold mb-4">
                {t('courses.welcome.readyToStart') || 'Ready to Start Your Journey?'}
              </h2>
              
              <p className="text-muted-foreground text-lg mb-8 max-w-2xl mx-auto">
                {t('courses.welcome.ctaDesc') || 'Join thousands of entrepreneurs who have transformed their ideas into successful businesses with InnoHub.'}
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/signup">
                  <Button size="lg" className="bg-primary hover:bg-primary/90 flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    {t('courses.welcome.joinNow') || 'Join Now'}
                  </Button>
                </Link>
                
                <Link href="/auth/login">
                  <Button variant="outline" size="lg" className="border-primary/20 hover:bg-primary/10">
                    {t('courses.welcome.signIn') || 'Sign In'}
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AnimatedGradientBackground>
  );
}
