'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Clock, 
  BookOpen, 
  Star, 
  Search,
  Filter,
  PlayCircle,
  Users
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import ProtectedRoute from '@/components/courses/ProtectedRoute';
import CoursesNavbar from '@/components/courses/CoursesNavbar';
import CouponInput from '@/components/courses/CouponInput';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import courseService from '@/lib/services/courseService';

// All courses data with proper thumbnails
const ALL_COURSES = [
  {
    id: 1,
    title: 'Entrepreneurship Fundamentals',
    description: 'Learn the fundamentals of starting and growing a successful business',
    thumbnail: '/images/courses/entrepreneurship.jpg',
    duration: '1h 29m',
    lessons: 8,
    level: 'Beginner',
    instructor: 'Otgonbaatar Tsedendemberel',
    category: 'Business',
    videoId: 'hlCYcATi0m4',
    rating: 4.8,
    students: 1250,
    price: 99,
    originalPrice: 99
  },
  {
    id: 2,
    title: 'Innovation Hub Insights',
    description: 'Discover how innovation hubs work and their role in startup ecosystem',
    thumbnail: '/images/courses/innovation.jpg',
    duration: '45m',
    lessons: 6,
    level: 'Beginner',
    instructor: 'Innovation Team',
    category: 'Business',
    videoId: 'esjWT-D9OF8',
    rating: 4.7,
    students: 980,
    price: 79,
    originalPrice: 79
  },
  {
    id: 3,
    title: 'Startup Funding Masterclass',
    description: 'Complete guide to raising capital and securing investment for your startup',
    thumbnail: '/images/courses/funding.jpg',
    duration: '18m',
    lessons: 5,
    level: 'Intermediate',
    instructor: 'Investment Experts',
    category: 'Finance',
    videoId: 'dQw4w9WgXcQ',
    rating: 4.9,
    students: 750,
    price: 149,
    originalPrice: 149
  },
  {
    id: 4,
    title: 'Digital Marketing for Startups',
    description: 'Master digital marketing strategies to grow your startup on a budget',
    thumbnail: '/images/courses/marketing.jpg',
    duration: '2h 15m',
    lessons: 12,
    level: 'Beginner',
    instructor: 'Sarah Chen',
    category: 'Marketing',
    videoId: 'gElfIo6uw4g',
    rating: 4.6,
    students: 1100,
    price: 'Free'
  },
  {
    id: 5,
    title: 'Product Development & Design',
    description: 'From idea to market: comprehensive product development methodology',
    thumbnail: '/images/courses/product.jpg',
    duration: '3h 45m',
    lessons: 15,
    level: 'Intermediate',
    instructor: 'Alex Rodriguez',
    category: 'Product',
    videoId: 'gElfIo6uw4g',
    rating: 4.8,
    students: 890,
    price: 'Free'
  },
  {
    id: 6,
    title: 'Building Successful Teams',
    description: 'Learn how to recruit, manage, and scale high-performing startup teams',
    thumbnail: '/images/courses/teams.jpg',
    duration: '1h 45m',
    lessons: 10,
    level: 'Intermediate',
    instructor: 'Michael Johnson',
    category: 'Business',
    videoId: 'dQw4w9WgXcQ',
    rating: 4.7,
    students: 670,
    price: 'Free'
  },
  {
    id: 7,
    title: 'Legal Basics for Startups',
    description: 'Essential legal knowledge every entrepreneur needs to protect their business',
    thumbnail: '/images/courses/legal.jpg',
    duration: '1h 30m',
    lessons: 8,
    level: 'Beginner',
    instructor: 'Jennifer Lee',
    category: 'Business',
    videoId: 'hlCYcATi0m4',
    rating: 4.5,
    students: 540,
    price: 'Free'
  },
  {
    id: 8,
    title: 'Scaling Your Business',
    description: 'Strategies and frameworks for scaling your startup to the next level',
    thumbnail: '/images/courses/scaling.jpg',
    duration: '2h 30m',
    lessons: 11,
    level: 'Advanced',
    instructor: 'David Park',
    category: 'Business',
    videoId: 'esjWT-D9OF8',
    rating: 4.9,
    students: 420,
    price: 'Free'
  },
  {
    id: 9,
    title: 'Financial Management',
    description: 'Essential financial skills every entrepreneur needs to know',
    thumbnail: '/images/courses/finance.jpg',
    duration: '2h 10m',
    lessons: 9,
    level: 'Intermediate',
    instructor: 'Financial Advisors',
    category: 'Finance',
    videoId: 'dQw4w9WgXcQ',
    rating: 4.6,
    students: 780,
    price: 'Free'
  },
  {
    id: 10,
    title: 'Innovation Methodologies',
    description: 'Learn proven innovation frameworks and methodologies for business growth',
    thumbnail: '/images/courses/innovation-methods.jpg',
    duration: '1h 50m',
    lessons: 7,
    level: 'Advanced',
    instructor: 'Innovation Consultants',
    category: 'Product',
    videoId: 'hlCYcATi0m4',
    rating: 4.8,
    students: 350,
    price: 'Free'
  }
];

const CATEGORIES = ['All', 'Business', 'Marketing', 'Finance', 'Product'];
const LEVELS = ['All', 'Beginner', 'Intermediate', 'Advanced'];

export default function CatalogPage() {
  const { t, isLoaded } = useLanguage();
  const [courses, setCourses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedLevel, setSelectedLevel] = useState('All');
  const [selectedCourse, setSelectedCourse] = useState<any>(null);
  const [showEnrollModal, setShowEnrollModal] = useState(false);
  const [coursePrice, setCoursePrice] = useState<number>(0);

  // Load courses from database
  useEffect(() => {
    const loadCourses = async () => {
      try {
        setIsLoading(true);
        const coursesData = await courseService.getAllCourses();
        setCourses(coursesData);
      } catch (error) {
        console.error('Failed to load courses:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCourses();
  }, []);

  useEffect(() => {
    filterCourses();
  }, [searchTerm, selectedCategory, selectedLevel]);

  const filterCourses = async () => {
    try {
      setIsLoading(true);
      const filters = {
        category: selectedCategory !== 'All' ? selectedCategory : undefined,
        level: selectedLevel !== 'All' ? selectedLevel.toUpperCase() : undefined,
      };

      const filtered = await courseService.searchCourses(searchTerm, filters);
      setCourses(filtered);
    } catch (error) {
      console.error('Failed to filter courses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getLevelColor = (level: string) => {
    const colors: { [key: string]: string } = {
      'Beginner': 'bg-green-500/20 text-green-300 border-green-500/30',
      'Intermediate': 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30',
      'Advanced': 'bg-red-500/20 text-red-300 border-red-500/30'
    };
    return colors[level] || 'bg-gray-500/20 text-gray-300 border-gray-500/30';
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Business': 'bg-purple-500/20 text-purple-300',
      'Marketing': 'bg-green-500/20 text-green-300',
      'Finance': 'bg-blue-500/20 text-blue-300',
      'Product': 'bg-orange-500/20 text-orange-300'
    };
    return colors[category] || 'bg-gray-500/20 text-gray-300';
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-black via-purple-900/20 to-black">
        <CoursesNavbar />
        <div className="pt-8 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold text-white mb-4">
              Course Catalog
            </h1>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Explore our comprehensive collection of entrepreneurship and business courses designed to accelerate your startup journey.
            </p>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
            className="mb-8"
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  {/* Search */}
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search courses, instructors, or topics..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 bg-black/40 border-primary/20 text-white placeholder-gray-400"
                      />
                    </div>
                  </div>

                  {/* Category Filter */}
                  <div className="flex gap-2">
                    {CATEGORIES.map((category) => (
                      <Button
                        key={category}
                        variant={selectedCategory === category ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedCategory(category)}
                        className={selectedCategory === category 
                          ? "bg-primary text-white" 
                          : "border-primary/30 text-primary hover:bg-primary/10"
                        }
                      >
                        {category}
                      </Button>
                    ))}
                  </div>

                  {/* Level Filter */}
                  <div className="flex gap-2">
                    {LEVELS.map((level) => (
                      <Button
                        key={level}
                        variant={selectedLevel === level ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedLevel(level)}
                        className={selectedLevel === level 
                          ? "bg-primary text-white" 
                          : "border-primary/30 text-primary hover:bg-primary/10"
                        }
                      >
                        {level}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Results Count */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="mb-6"
          >
            <p className="text-gray-400">
              {isLoading ? 'Loading courses...' : `Showing ${courses.length} courses`}
            </p>
          </motion.div>

          {/* Course Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
                    <div className="aspect-video bg-gray-700/50 rounded-t-lg"></div>
                    <CardContent className="p-4 space-y-3">
                      <div className="h-4 bg-gray-700/50 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-700/50 rounded w-full"></div>
                      <div className="h-3 bg-gray-700/50 rounded w-2/3"></div>
                      <div className="flex justify-between items-center">
                        <div className="h-3 bg-gray-700/50 rounded w-1/4"></div>
                        <div className="h-8 bg-gray-700/50 rounded w-1/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))
            ) : courses.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-400 text-lg">No courses found matching your criteria.</p>
              </div>
            ) : (
              courses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index, duration: 0.5 }}
              >
                <Card className="border-primary/20 bg-black/60 backdrop-blur-md hover:border-primary/40 transition-all duration-300 overflow-hidden group cursor-pointer">
                  {/* Course Thumbnail */}
                  <div className="relative aspect-video overflow-hidden">
                    <Image
                      src={`https://img.youtube.com/vi/${course.videoId}/maxresdefault.jpg`}
                      alt={course.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = course.thumbnail || 'https://placehold.co/600x400/3a0647/ffffff?text=InnoHub+Course';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <PlayCircle className="h-16 w-16 text-white" />
                    </div>

                    {/* Course Level Badge */}
                    <div className="absolute top-3 left-3">
                      <Badge className={`${getLevelColor(course.level)} text-xs`}>
                        {course.level}
                      </Badge>
                    </div>

                    {/* Course Price */}
                    <div className="absolute top-3 right-3">
                      <Badge className="bg-purple-500/90 text-white text-xs">
                        ${typeof course.price === 'number' ? course.price : course.price}
                      </Badge>
                    </div>

                    {/* Duration */}
                    <div className="absolute bottom-3 right-3 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {course.duration}
                    </div>
                  </div>

                  <CardContent className="p-6">
                    {/* Category */}
                    <div className="mb-3">
                      <Badge className={`${getCategoryColor(course.category)} text-xs`}>
                        {course.category}
                      </Badge>
                    </div>

                    {/* Title and Description */}
                    <h3 className="font-semibold text-lg text-white mb-2 group-hover:text-primary transition-colors">
                      {course.title}
                    </h3>
                    <p className="text-gray-400 text-sm mb-4 line-clamp-2">
                      {course.description}
                    </p>

                    {/* Instructor */}
                    <div className="flex items-center gap-2 mb-4">
                      <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                        <Users className="h-3 w-3 text-primary" />
                      </div>
                      <span className="text-sm text-gray-400">{course.instructor}</span>
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="text-sm text-gray-400">{course.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <BookOpen className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-400">{course.lessons} lessons</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-400">{course.students}</span>
                      </div>
                    </div>

                    {/* Action Button */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-white">
                          ${typeof course.price === 'number' ? course.price : 'Free'}
                        </span>
                        {typeof course.price === 'number' && course.price > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedCourse(course);
                              setCoursePrice(course.price);
                              setShowEnrollModal(true);
                            }}
                            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                          >
                            Apply Coupon
                          </Button>
                        )}
                      </div>
                      <Button
                        className="w-full bg-primary/20 text-primary hover:bg-primary hover:text-white border-primary/30 transition-all"
                        onClick={() => window.location.href = `/courses/${course.id}`}
                      >
                        {typeof course.price === 'number' && course.price > 0 ? 'Enroll Now' : 'Start Learning'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
            )}
          </motion.div>


        </div>
        </div>
      </div>

      {/* Course Enrollment Modal */}
      <Dialog open={showEnrollModal} onOpenChange={setShowEnrollModal}>
        <DialogContent className="bg-black/95 border-purple-500/20 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-white">
              Enroll in {selectedCourse?.title}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">
                ${coursePrice}
              </div>
              <p className="text-gray-400">Course Price</p>
            </div>

            <CouponInput
              courseId={selectedCourse?.id?.toString()}
              coursePrice={coursePrice}
              onCouponApplied={(result) => {
                if (result.finalPrice !== undefined) {
                  setCoursePrice(result.finalPrice);
                }
              }}
              onCouponRemoved={() => {
                setCoursePrice(selectedCourse?.price || 0);
              }}
            />

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowEnrollModal(false)}
                className="flex-1 border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  // Handle enrollment logic here
                  window.location.href = `/courses/${selectedCourse?.id}`;
                }}
                className="flex-1 bg-purple-500 hover:bg-purple-600 text-white"
              >
                {coursePrice === 0 ? 'Start Learning' : `Enroll for $${coursePrice}`}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </ProtectedRoute>
  );
}
