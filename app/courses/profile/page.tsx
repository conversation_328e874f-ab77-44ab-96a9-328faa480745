'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  BookOpen, 
  Clock, 
  Award, 
  TrendingUp, 
  Calendar,
  Target,
  Star,
  LogOut,
  Settings,
  Download
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { getCurrentUser, logout } from '@/lib/auth';
import ProtectedRoute from '@/components/courses/ProtectedRoute';
import CoursesNavbar from '@/components/courses/CoursesNavbar';
import CouponDashboard from '@/components/courses/CouponDashboard';

interface CourseProgress {
  id: number;
  title: string;
  progress: number;
  totalLessons: number;
  completedLessons: number;
  lastAccessed: string;
  category: string;
  thumbnail: string;
}

export default function ProfilePage() {
  const { t, isLoaded } = useLanguage();
  const [user, setUser] = useState<any>(null);
  const [courseProgress, setCourseProgress] = useState<CourseProgress[]>([]);
  const [totalStats, setTotalStats] = useState({
    coursesStarted: 0,
    coursesCompleted: 0,
    totalHoursLearned: 0,
    averageProgress: 0,
    streak: 0,
    certificates: 0
  });

  useEffect(() => {
    const currentUser = getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
      loadUserProgress();
    }
  }, []);

  const loadUserProgress = () => {
    // Mock course progress data - in real app, this would come from API
    const mockProgress: CourseProgress[] = [
      {
        id: 1,
        title: 'Entrepreneurship Fundamentals',
        progress: 75,
        totalLessons: 8,
        completedLessons: 6,
        lastAccessed: '2024-01-15',
        category: 'Business',
        thumbnail: 'hlCYcATi0m4'
      },
      {
        id: 2,
        title: 'Innovation Hub Insights',
        progress: 50,
        totalLessons: 6,
        completedLessons: 3,
        lastAccessed: '2024-01-14',
        category: 'Business',
        thumbnail: 'esjWT-D9OF8'
      },
      {
        id: 3,
        title: 'Startup Funding Masterclass',
        progress: 100,
        totalLessons: 5,
        completedLessons: 5,
        lastAccessed: '2024-01-10',
        category: 'Finance',
        thumbnail: 'dQw4w9WgXcQ'
      },
      {
        id: 4,
        title: 'Digital Marketing for Startups',
        progress: 25,
        totalLessons: 12,
        completedLessons: 3,
        lastAccessed: '2024-01-12',
        category: 'Marketing',
        thumbnail: 'gElfIo6uw4g'
      }
    ];

    setCourseProgress(mockProgress);

    // Calculate total stats
    const coursesStarted = mockProgress.length;
    const coursesCompleted = mockProgress.filter(course => course.progress === 100).length;
    const totalHoursLearned = 24; // Mock data
    const averageProgress = Math.round(mockProgress.reduce((acc, course) => acc + course.progress, 0) / coursesStarted);
    const streak = 7; // Mock data
    const certificates = coursesCompleted;

    setTotalStats({
      coursesStarted,
      coursesCompleted,
      totalHoursLearned,
      averageProgress,
      streak,
      certificates
    });
  };

  const handleLogout = () => {
    logout();
    window.location.href = '/';
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'text-green-500';
    if (progress >= 75) return 'text-blue-500';
    if (progress >= 50) return 'text-yellow-500';
    return 'text-gray-500';
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Business': 'bg-purple-500/20 text-purple-300',
      'Marketing': 'bg-green-500/20 text-green-300',
      'Finance': 'bg-blue-500/20 text-blue-300',
      'Product': 'bg-orange-500/20 text-orange-300'
    };
    return colors[category] || 'bg-gray-500/20 text-gray-300';
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-black via-purple-900/20 to-black">
        <CoursesNavbar />
        <div className="pt-8 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Profile Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
                  <div className="flex items-center gap-6">
                    <div className="w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                      <User className="h-10 w-10 text-white" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold text-white mb-2">
                        {user?.name || 'User'}
                      </h1>
                      <p className="text-gray-400 mb-2">{user?.email}</p>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-primary/20 text-primary border-primary/30">
                          <Star className="h-3 w-3 mr-1" />
                          Premium Learner
                        </Badge>
                        <Badge className="bg-green-500/20 text-green-300 border-green-500/30">
                          <Target className="h-3 w-3 mr-1" />
                          {totalStats.streak} Day Streak
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-3">
                    <Button variant="outline" className="border-primary/30 text-primary hover:bg-primary/10">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Button>
                    <Button 
                      variant="outline" 
                      className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                      onClick={handleLogout}
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Stats Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
            className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8"
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <BookOpen className="h-8 w-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{totalStats.coursesStarted}</div>
                <div className="text-sm text-gray-400">Courses Started</div>
              </CardContent>
            </Card>
            
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <Award className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{totalStats.coursesCompleted}</div>
                <div className="text-sm text-gray-400">Completed</div>
              </CardContent>
            </Card>

            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <Clock className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{totalStats.totalHoursLearned}h</div>
                <div className="text-sm text-gray-400">Hours Learned</div>
              </CardContent>
            </Card>

            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <TrendingUp className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{totalStats.averageProgress}%</div>
                <div className="text-sm text-gray-400">Avg Progress</div>
              </CardContent>
            </Card>

            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <Calendar className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{totalStats.streak}</div>
                <div className="text-sm text-gray-400">Day Streak</div>
              </CardContent>
            </Card>

            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-4 text-center">
                <Download className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{totalStats.certificates}</div>
                <div className="text-sm text-gray-400">Certificates</div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Course Progress */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="mb-8"
          >
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <TrendingUp className="h-5 w-5 text-primary" />
                  Course Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {courseProgress.map((course) => (
                    <div key={course.id} className="flex items-center gap-4 p-4 rounded-lg bg-black/40 border border-primary/10">
                      {/* Course Thumbnail */}
                      <div className="relative w-16 h-12 rounded-lg overflow-hidden bg-muted/20 flex-shrink-0">
                        <img
                          src={`https://img.youtube.com/vi/${course.thumbnail}/mqdefault.jpg`}
                          alt={course.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = 'https://placehold.co/320x180/3a0647/ffffff?text=Course';
                          }}
                        />
                        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                          <BookOpen className="h-4 w-4 text-white" />
                        </div>
                      </div>

                      {/* Course Info */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="font-semibold text-white text-sm">{course.title}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge className={`text-xs ${getCategoryColor(course.category)}`}>
                                {course.category}
                              </Badge>
                              <span className="text-xs text-gray-400">
                                Last accessed: {new Date(course.lastAccessed).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <div className={`text-right ${getProgressColor(course.progress)}`}>
                            <div className="text-lg font-bold">{course.progress}%</div>
                            <div className="text-xs text-gray-400">
                              {course.completedLessons}/{course.totalLessons} lessons
                            </div>
                          </div>
                        </div>

                        <Progress
                          value={course.progress}
                          className="h-2 bg-gray-700"
                        />
                      </div>

                      {/* Action Button */}
                      <Button
                        size="sm"
                        className="bg-primary/20 text-primary hover:bg-primary/30 border-primary/30"
                        onClick={() => window.location.href = `/courses/${course.id}`}
                      >
                        {course.progress === 100 ? 'Review' : 'Continue'}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Achievements & Certificates */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
          >
            {/* Achievements */}
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Award className="h-5 w-5 text-yellow-500" />
                  Achievements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
                    <div className="w-10 h-10 rounded-full bg-yellow-500/20 flex items-center justify-center">
                      <Star className="h-5 w-5 text-yellow-500" />
                    </div>
                    <div>
                      <div className="font-semibold text-white">First Course Completed</div>
                      <div className="text-sm text-gray-400">Completed your first course</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 rounded-lg bg-blue-500/10 border border-blue-500/20">
                    <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-blue-500" />
                    </div>
                    <div>
                      <div className="font-semibold text-white">Week Streak</div>
                      <div className="text-sm text-gray-400">7 days of continuous learning</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-3 rounded-lg bg-purple-500/10 border border-purple-500/20">
                    <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                      <BookOpen className="h-5 w-5 text-purple-500" />
                    </div>
                    <div>
                      <div className="font-semibold text-white">Knowledge Seeker</div>
                      <div className="text-sm text-gray-400">Started 4 different courses</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Certificates */}
            <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Download className="h-5 w-5 text-green-500" />
                  Certificates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                        <Award className="h-5 w-5 text-green-500" />
                      </div>
                      <div>
                        <div className="font-semibold text-white">Startup Funding Masterclass</div>
                        <div className="text-sm text-gray-400">Completed on Jan 10, 2024</div>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" className="border-green-500/30 text-green-400 hover:bg-green-500/10">
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </div>

                  <div className="flex items-center justify-center p-8 rounded-lg bg-gray-500/10 border border-gray-500/20 border-dashed">
                    <div className="text-center">
                      <Award className="h-8 w-8 text-gray-500 mx-auto mb-2" />
                      <div className="text-gray-400">Complete more courses to earn certificates</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Coupon Dashboard */}
            <CouponDashboard />
          </motion.div>
        </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
