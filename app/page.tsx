'use client';

import HeroSection from "@/components/home/<USER>";
import BaselineSection from "@/components/home/<USER>";
import ServiceIntroductionSection from "@/components/home/<USER>";
import FeatureCards from "@/components/home/<USER>";
import GrowthSection from "@/components/home/<USER>";
import TeamSection from "@/components/home/<USER>";
import ContactSection from "@/components/home/<USER>";
import ProjectsSection from "@/components/home/<USER>";
import TextHoverEffectDemo from "@/components/ui/text-hover-effect-demo";
import { SmoothScrollProvider } from "@/components/ui/smooth-scroll-provider";
import { ParallaxSection } from "@/components/ui/parallax-section";
import { SmoothReveal } from "@/components/ui/smooth-reveal";
import { ScrollToTop } from "@/components/ui/scroll-to-top";
import { motion, useScroll, useTransform } from "framer-motion";
import { useRef } from "react";

export default function Home() {
  const mainRef = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: mainRef,
    offset: ["start start", "end end"]
  });

  // Subtle background color shift as user scrolls
  const backgroundColor = useTransform(
    scrollYProgress,
    [0, 0.3, 0.6, 1],
    [
      "rgb(0, 0, 0)",
      "rgb(10, 5, 20)",
      "rgb(15, 10, 30)",
      "rgb(5, 5, 15)"
    ]
  );

  return (
    <SmoothScrollProvider>
      <motion.main
        ref={mainRef}
        className="bg-black text-white relative"
        style={{ backgroundColor }}
      >
        {/* Scroll to top button */}
        <ScrollToTop />

        {/* Background Text Effect - Footer Area Only */}
        <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[-10] flex items-center justify-center pointer-events-none">
          <div className="pointer-events-auto">
            <TextHoverEffectDemo />
          </div>
        </div>

        {/* Page Content with Smooth Animations */}
        <HeroSection />

        <SmoothReveal>
          <BaselineSection />
        </SmoothReveal>

        <ParallaxSection direction="up" speed={0.1}>
          <ServiceIntroductionSection />
        </ParallaxSection>

        <SmoothReveal delay={0.1}>
          <FeatureCards />
        </SmoothReveal>

        <ParallaxSection direction="down" speed={0.15}>
          <GrowthSection />
        </ParallaxSection>

        <SmoothReveal>
          <TeamSection />
        </SmoothReveal>

        <ParallaxSection direction="up" speed={0.1}>
          <ProjectsSection />
        </ParallaxSection>

        <SmoothReveal direction="up">
          <ContactSection />
        </SmoothReveal>
      </motion.main>
    </SmoothScrollProvider>
  );
}
