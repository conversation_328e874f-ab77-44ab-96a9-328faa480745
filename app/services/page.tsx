'use client';

import { motion } from 'framer-motion';
import { AnimatedText } from '@/components/ui/animated-text';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { AnimatedCard } from '@/components/ui/animated-card';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

const services = [
  {
    title: 'AI Consulting',
    description: 'Strategic guidance to identify high-potential AI use cases for your business.',
    icon: '🧠',
    link: '/services/consulting',
  },
  {
    title: 'Data Collection & Integration',
    description: 'Consolidate and prepare your data for AI model training and deployment.',
    icon: '📊',
    link: '/services/data-integration',
  },
  {
    title: 'Prototype Development',
    description: 'Rapidly test AI solutions with iterative prototyping on your actual data.',
    icon: '⚙️',
    link: '/services/prototype',
  },
  {
    title: 'Enterprise Deployment',
    description: 'Scale your AI solutions with robust MLOps and integration support.',
    icon: '🚀',
    link: '/services/deployment',
  },
  {
    title: 'AI Training & Workshops',
    description: 'Empower your team with the knowledge to leverage AI effectively.',
    icon: '👨‍🏫',
    link: '/services/training',
  },
  {
    title: 'Custom AI Solutions',
    description: 'Tailored AI applications designed specifically for your business needs.',
    icon: '🔧',
    link: '/services/custom-solutions',
  },
  {
    title: 'AI Strategy Development',
    description: 'Comprehensive roadmap for integrating AI into your business operations.',
    icon: '📝',
    link: '/services/strategy',
  },
  {
    title: 'Ongoing Support & Maintenance',
    description: 'Continuous optimization and support for your AI implementations.',
    icon: '🔄',
    link: '/services/support',
  },
];

export default function ServicesPage() {
  return (
    <>
      <AnimatedGradientBackground className="min-h-[40vh] flex flex-col items-center justify-center pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-4"
            >
              Our Services
            </motion.div>
            
            <AnimatedText
              text="Comprehensive AI Solutions for Your Business"
              className="text-4xl md:text-5xl font-bold tracking-tight mb-6"
            />
            
            <AnimatedText
              text="From strategy to implementation, we provide end-to-end AI services tailored to your specific needs."
              className="text-xl text-muted-foreground"
              once
            />
          </div>
        </div>
      </AnimatedGradientBackground>
      
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <TextReveal className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-4">
              Our Service Offerings
            </h2>
            <p className="text-muted-foreground">
              Explore our comprehensive range of AI services designed to transform your business operations and drive innovation.
            </p>
          </TextReveal>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
            {services.map((service, index) => (
              <AnimatedCard key={service.title} className="h-full">
                <Card className="h-full flex flex-col">
                  <CardHeader>
                    <div className="text-4xl mb-4">{service.icon}</div>
                    <CardTitle>{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow">
                    <CardDescription className="text-base">{service.description}</CardDescription>
                  </CardContent>
                  <CardFooter>
                    <Button variant="ghost" className="group" asChild>
                      <Link href={service.link}>
                        Learn more
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              </AnimatedCard>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}
