import type { Metadata } from "next";
import "./globals.css";
import "./timeline-effects.css";
import ClientLayout from "./client-layout";
import SessionProvider from "@/components/providers/SessionProvider";

export const metadata: Metadata = {
  title: "InnoHub - Innovation Hub for Startups",
  description: "Accelerating the future of innovation with cutting-edge resources and mentorship",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <SessionProvider>
      <ClientLayout>{children}</ClientLayout>
    </SessionProvider>
  );
}
