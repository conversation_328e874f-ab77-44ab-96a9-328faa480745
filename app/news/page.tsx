'use client';

import { useState, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { AnimatedText } from '@/components/ui/animated-text';
import { GlowingStarsBackground } from '@/components/ui/aceternity/glowing-stars';
import { Spotlight } from '@/components/ui/aceternity/spotlight';
import { LightbulbPlant } from '@/components/ui/aceternity/lightbulb-plant';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AnimatedCard } from '@/components/ui/animated-card';
import { ArrowRight, Calendar, Search, Filter, Lightbulb, TrendingUp, Clock } from 'lucide-react';
import TextHoverEffectDemo from '@/components/ui/text-hover-effect-demo';
import { useLanguage } from '@/lib/context/language-context';

// Define the type for news articles
interface NewsArticle {
  id: number;
  title: string;
  excerpt: string;
  date: string;
  category: string;
  image: string;
  slug: string;
  featured?: boolean;
  readTime?: string;
}

const categories = ['All', 'Program', 'Success Story', 'Company News', 'Events', 'Partnership'];

// Enhanced NewsCard component
const NewsCard = ({ article, index }: { article: NewsArticle; index: number }) => {
  return (
    <AnimatedCard
      variant="glass"
      hoverEffect="lift"
      delay={index * 0.1}
      className="h-full group"
    >
      <Link href={`/news/${article.slug}`} className="h-full block">
        <div className="relative h-full flex flex-col">
          {/* Header image */}
          <div className="relative aspect-[16/9] overflow-hidden">
            <Image
              src={article.image}
              alt={article.title}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

            {/* Category badge */}
            <div className="absolute top-4 left-4">
              <span className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium transition-colors border-primary/30 bg-primary/20 text-primary backdrop-blur-sm">
                {article.category}
              </span>
            </div>

            {/* Featured badge */}
            {article.featured && (
              <div className="absolute top-4 right-4">
                <motion.div
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ delay: index * 0.1 + 0.3, type: "spring" }}
                  className="px-2 py-1 bg-yellow-500/20 text-yellow-400 text-xs font-medium rounded-full border border-yellow-500/30 backdrop-blur-sm"
                >
                  Featured
                </motion.div>
              </div>
            )}

            {/* Lightbulb hover effect */}
            <motion.div
              className="absolute -top-6 left-1/2 -translate-x-1/2 w-12 h-12 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ y: 10 }}
              whileHover={{ y: 0 }}
            >
              <div className="relative w-full h-full">
                <motion.div
                  className="absolute inset-0 rounded-full bg-yellow-300/30 blur-md"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 0.8, 0.5]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: 'reverse',
                  }}
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <Lightbulb className="h-6 w-6 text-yellow-300" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* Content */}
          <div className="flex-grow p-6">
            <div className="flex items-center gap-3 mb-3 text-xs text-white/60">
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                {article.date}
              </div>
              {article.readTime && (
                <div className="flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  {article.readTime}
                </div>
              )}
            </div>

            <h3 className="font-bold text-lg mb-3 group-hover:text-primary transition-colors duration-300 line-clamp-2">
              {article.title}
            </h3>

            <p className="text-white/70 text-sm leading-relaxed line-clamp-3">
              {article.excerpt}
            </p>
          </div>

          {/* Footer */}
          <div className="p-6 pt-0">
            <div className="flex items-center justify-between">
              <Button variant="ghost" size="sm" className="group p-0 h-auto text-primary hover:text-primary">
                Read More
                <ArrowRight className="ml-2 h-3 w-3 transition-transform group-hover:translate-x-1" />
              </Button>

              <div className="flex items-center text-xs text-white/40">
                <TrendingUp className="h-3 w-3 mr-1" />
                Trending
              </div>
            </div>
          </div>
        </div>
      </Link>
    </AnimatedCard>
  );
};
export default function NewsPage() {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('All');
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0.8, 1, 1, 0.8]);

  const newsArticles: NewsArticle[] = [
    {
      id: 1,
      title: t('news.article1.title'),
      excerpt: t('news.article1.excerpt'),
      date: 'May 15, 2024',
      category: 'Finance',
      image: '/images/programs/1.jpg',
      slug: 'market-analysis',
      featured: true,
      readTime: '5 min read',
    },
    {
      id: 2,
      title: t('news.article2.title'),
      excerpt: t('news.article2.excerpt'),
      date: 'May 10, 2024',
      category: 'Finance',
      image: '/images/programs/2.jpg',
      slug: 'mongolia-finance-analysis',
      readTime: '3 min read',
    },
    {
      id: 3,
      title: t('news.article3.title'),
      excerpt: t('news.article3.excerpt'),
      date: 'May 5, 2024',
      category: 'Data',
      image: '/images/programs/plant-bulbs.jpg',
      slug: 'data-processing-solongo',
      readTime: '7 min read',
    },
    {
      id: 4,
      title: t('news.article4.title'),
      excerpt: t('news.article4.excerpt'),
      date: 'April 28, 2024',
      category: 'International',
      image: '/images/programs/1.jpg',
      slug: 'international-market-entry',
      readTime: '4 min read',
    },
  ];

  const featuredArticle = {
    id: 5,
    title: t('news.featured.title'),
    excerpt: t('news.featured.excerpt'),
    date: 'June 5, 2024',
    category: 'Marketing',
    image: '/images/programs/2.jpg',
    slug: 'product-marketing-vs-marketing',
    featured: true,
    readTime: '6 min read',
  };

  // Filter articles based on search and category
  const filteredArticles = newsArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'All' || article.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-black">
      {/* Background Text Effect - Footer Area Only */}
      <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[-10] flex items-center justify-center pointer-events-none">
        <div className="pointer-events-auto">
          <TextHoverEffectDemo />
        </div>
      </div>

      {/* Hero Section */}
      <GlowingStarsBackground className="min-h-[70vh] flex flex-col items-center justify-center pt-32 pb-20 relative overflow-hidden">
        <Spotlight className="absolute inset-0">
          <div></div>
        </Spotlight>

        <motion.div
          ref={containerRef}
          style={{ opacity, scale }}
          className="container mx-auto px-4 relative z-10"
        >
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6"
            >
              News & Insights
            </motion.div>

            <AnimatedText
              text="Illuminating Innovation Stories"
              className="text-4xl md:text-6xl font-bold tracking-tight mb-6"
            />

            <AnimatedText
              text="Stay updated with our latest announcements, success stories, and upcoming events that shape the future of innovation."
              className="text-xl text-white/70 leading-relaxed"
              once
            />

            <div className="mt-12 flex justify-center">
              <div className="relative w-full max-w-md">
                <LightbulbPlant className="absolute -top-24 left-1/2 -translate-x-1/2 w-32 h-32 opacity-70" />
              </div>
            </div>
          </div>
        </motion.div>
      </GlowingStarsBackground>

      {/* Search and Filter Section */}
      <section className="py-12 bg-gradient-to-b from-black via-black/95 to-black relative">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[length:50px_50px]" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="mb-12 max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between bg-black/60 backdrop-blur-md p-6 rounded-xl border border-white/10">
              <div className="relative w-full md:w-auto flex-grow">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-white/40" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  className="pl-10 bg-black/50 border-white/10 text-white placeholder:text-white/40"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2 w-full md:w-auto overflow-x-auto pb-2 md:pb-0">
                <Filter className="h-4 w-4 text-primary hidden md:block" />
                <div className="flex gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={activeCategory === category ? "default" : "outline"}
                      onClick={() => setActiveCategory(category)}
                      size="sm"
                      className={`whitespace-nowrap transition-all duration-300 ${
                        activeCategory === category
                          ? 'bg-primary/20 text-primary border-primary/30 hover:bg-primary/30'
                          : 'bg-black/50 border-white/10 text-white/70 hover:bg-white/5 hover:text-white'
                      }`}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* News Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {filteredArticles.map((article, index) => (
              <NewsCard key={article.id} article={article} index={index} />
            ))}
          </div>

          {/* Featured Article */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto"
          >
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-white mb-2">Featured Article</h2>
              <p className="text-white/70">Don't miss our most important story of the week</p>
            </div>

            <AnimatedCard variant="glow" hoverEffect="lift" className="overflow-hidden">
              <Link href={`/news/${featuredArticle.slug}`}>
                <div className="flex flex-col md:flex-row">
                  <div className="md:w-1/2 relative aspect-[16/9] md:aspect-auto">
                    <Image
                      src={featuredArticle.image}
                      alt={featuredArticle.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center gap-3 mb-4">
                      <span className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium border-primary/30 bg-primary/20 text-primary">
                        {featuredArticle.category}
                      </span>
                      <div className="flex items-center text-xs text-white/60">
                        <Calendar className="h-3 w-3 mr-1" />
                        {featuredArticle.date}
                      </div>
                      <div className="flex items-center text-xs text-white/60">
                        <Clock className="h-3 w-3 mr-1" />
                        {featuredArticle.readTime}
                      </div>
                    </div>

                    <h3 className="text-2xl font-bold text-white mb-4 hover:text-primary transition-colors duration-300">
                      {featuredArticle.title}
                    </h3>

                    <p className="text-white/70 leading-relaxed mb-6">
                      {featuredArticle.excerpt}
                    </p>

                    <Button className="group">
                      Read Full Article
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Button>
                  </div>
                </div>
              </Link>
            </AnimatedCard>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
