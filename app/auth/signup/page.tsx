'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Auth<PERSON><PERSON>, <PERSON>th<PERSON>ard<PERSON>ontent, AuthCard<PERSON>ooter, AuthCardHeader, AuthCardTitle, AuthCardDescription } from '@/components/ui/auth-card';
import { AuthButton } from '@/components/ui/auth-button';
import { Checkbox } from '@/components/ui/checkbox';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { signup } from '@/lib/auth';
import { Eye, EyeOff, Github, Mail, User, UserPlus } from 'lucide-react';

export default function SignupPage() {
  const { t, isLoaded } = useLanguage();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    agreeToTerms: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, agreeToTerms: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await signup(formData.name, formData.email, formData.password);
      // Redirect to onboarding for new users
      router.push('/onboarding');
    } catch (err) {
      setError('Failed to create account. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatedGradientBackground className="min-h-screen flex items-center justify-center py-8 sm:py-12 lg:py-20">
      <div className="w-full max-w-md px-4 sm:px-6 lg:px-8">
        <AuthCard>
          <AuthCardHeader>
            <motion.div
              className="flex justify-center mb-6"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: [0.19, 1, 0.22, 1] }}
            >
              <div className="relative">
                <motion.div
                  className="absolute -inset-2 bg-gradient-to-r from-primary via-purple-500 to-primary rounded-full opacity-20 blur-lg"
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.1, 1],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                <div className="relative bg-primary/10 p-4 rounded-full border border-primary/20">
                  <UserPlus className="text-primary h-8 w-8" />
                </div>
              </div>
            </motion.div>
            <AuthCardTitle>
              {isLoaded ? t('auth.createAccount') : 'Create an account'}
            </AuthCardTitle>
            <AuthCardDescription>
              {isLoaded ? t('auth.enterDetails') : 'Enter your details to create your account'}
            </AuthCardDescription>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid gap-8">
              {error && (
                <motion.div
                  className="p-4 text-sm text-red-400 bg-red-400/10 border border-red-400/20 rounded-lg backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {error}
                </motion.div>
              )}
              <div className="grid gap-6">
                <motion.div
                  className="grid gap-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <Label htmlFor="name" className="text-white font-medium text-sm">
                    {isLoaded ? t('auth.name') : 'Name'}
                  </Label>
                  <div className="relative">
                    <User className="absolute left-4 top-4 h-4 w-4 text-muted-foreground z-10" />
                    <Input
                      enhanced
                      id="name"
                      name="name"
                      placeholder={isLoaded ? t('auth.namePlaceholder') : 'Your name'}
                      type="text"
                      autoCapitalize="words"
                      autoComplete="name"
                      className="pl-12"
                      value={formData.name}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </motion.div>
                <motion.div
                  className="grid gap-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <Label htmlFor="email" className="text-white font-medium text-sm">
                    {isLoaded ? t('auth.email') : 'Email'}
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-4 top-4 h-4 w-4 text-muted-foreground z-10" />
                    <Input
                      enhanced
                      id="email"
                      name="email"
                      placeholder={isLoaded ? t('auth.emailPlaceholder') : '<EMAIL>'}
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      className="pl-12"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </motion.div>

                <motion.div
                  className="grid gap-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  <Label htmlFor="password" className="text-white font-medium text-sm">
                    {isLoaded ? t('auth.password') : 'Password'}
                  </Label>
                  <div className="relative">
                    <motion.button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-4 text-muted-foreground hover:text-white transition-colors duration-200 z-10"
                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </motion.button>
                    <Input
                      enhanced
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoCapitalize="none"
                      autoComplete="new-password"
                      className="pr-12"
                      value={formData.password}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {isLoaded ? t('auth.passwordRequirements') : 'Password must be at least 8 characters long'}
                  </p>
                </motion.div>
                <motion.div
                  className="flex items-start space-x-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <Checkbox
                    id="terms"
                    checked={formData.agreeToTerms}
                    onCheckedChange={handleCheckboxChange}
                    required
                    className="border-primary/30 data-[state=checked]:bg-primary data-[state=checked]:border-primary mt-1"
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm font-medium leading-relaxed text-white cursor-pointer peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {isLoaded ? t('auth.agreeToTerms') : 'I agree to the'}{' '}
                    <Link
                      href="/terms"
                      className="text-primary hover:text-primary/80 underline underline-offset-4 transition-colors duration-200"
                    >
                      {isLoaded ? t('auth.termsOfService') : 'Terms of Service'}
                    </Link>{' '}
                    {isLoaded ? t('auth.and') : 'and'}{' '}
                    <Link
                      href="/privacy"
                      className="text-primary hover:text-primary/80 underline underline-offset-4 transition-colors duration-200"
                    >
                      {isLoaded ? t('auth.privacyPolicy') : 'Privacy Policy'}
                    </Link>
                  </label>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <AuthButton
                  className="w-full"
                  onClick={handleSubmit}
                  loading={isLoading}
                  disabled={isLoading || !formData.agreeToTerms}
                >
                  {isLoading ? (
                    isLoaded ? t('auth.creatingAccount') : 'Creating account...'
                  ) : (
                    isLoaded ? t('auth.createAccount') : 'Create account'
                  )}
                </AuthButton>
              </motion.div>
              <motion.div
                className="relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-primary/20"></span>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-black/80 px-3 text-muted-foreground">
                    {isLoaded ? t('auth.orContinueWith') : 'Or continue with'}
                  </span>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.9 }}
              >
                <AuthButton variant="outline" className="w-full">
                  <Github className="mr-2 h-4 w-4" />
                  Github
                </AuthButton>
              </motion.div>
            </div>
          </AuthCardContent>
          <AuthCardFooter>
            <motion.p
              className="text-center text-sm text-muted-foreground"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.0 }}
            >
              {isLoaded ? t('auth.alreadyHaveAccount') : 'Already have an account?'}{' '}
              <Link
                href="/auth/login"
                className="font-medium text-primary hover:text-primary/80 transition-colors duration-200 underline underline-offset-4"
              >
                {isLoaded ? t('auth.signIn') : 'Sign in'}
              </Link>
            </motion.p>
          </AuthCardFooter>
        </AuthCard>
      </div>
    </AnimatedGradientBackground>
  );
}
