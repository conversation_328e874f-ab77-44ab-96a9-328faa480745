'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Auth<PERSON><PERSON>, AuthCardContent, AuthCard<PERSON>ooter, AuthCardHeader, AuthCardTitle, AuthCardDescription } from '@/components/ui/auth-card';
import { AuthButton } from '@/components/ui/auth-button';
import { Checkbox } from '@/components/ui/checkbox';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { login } from '@/lib/auth';
import { Eye, EyeOff, Github, Mail, Sparkles } from 'lucide-react';

export default function LoginPage() {
  const { t, isLoaded } = useLanguage();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, rememberMe: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const user = await login(formData.email, formData.password);

      // Determine redirect path
      const redirectTo = searchParams.get('redirect') || '/courses';

      // If user is not onboarded, redirect to onboarding
      if (!user.isOnboarded) {
        router.push('/onboarding');
      } else {
        router.push(redirectTo);
      }
    } catch (err) {
      setError('Invalid email or password');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatedGradientBackground className="min-h-screen flex items-center justify-center py-8 sm:py-12 lg:py-20">
      <div className="w-full max-w-md px-4 sm:px-6 lg:px-8">
        <AuthCard>
          <AuthCardHeader>
            <motion.div
              className="flex justify-center mb-6"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: [0.19, 1, 0.22, 1] }}
            >
              <div className="relative">
                <motion.div
                  className="absolute -inset-2 bg-gradient-to-r from-primary via-purple-500 to-primary rounded-full opacity-20 blur-lg"
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.1, 1],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                <div className="relative bg-primary/10 p-4 rounded-full border border-primary/20">
                  <Sparkles className="text-primary h-8 w-8" />
                </div>
              </div>
            </motion.div>
            <AuthCardTitle>
              {isLoaded ? t('auth.loginCourse') : 'Access Your Courses'}
            </AuthCardTitle>
            <AuthCardDescription>
              {isLoaded ? t('auth.enterCredentialsCourse') : 'Enter your credentials to access exclusive courses and learning materials'}
            </AuthCardDescription>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid gap-8">
              {error && (
                <motion.div
                  className="p-4 text-sm text-red-400 bg-red-400/10 border border-red-400/20 rounded-lg backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {error}
                </motion.div>
              )}
              <div className="grid gap-6">
                <motion.div
                  className="grid gap-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <Label htmlFor="email" className="text-white font-medium text-sm">
                    {isLoaded ? t('auth.email') : 'Email'}
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-4 top-4 h-4 w-4 text-muted-foreground z-10" />
                    <Input
                      enhanced
                      id="email"
                      name="email"
                      placeholder={isLoaded ? t('auth.emailPlaceholder') : '<EMAIL>'}
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      className="pl-12"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </motion.div>
                <motion.div
                  className="grid gap-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password" className="text-white font-medium text-sm">
                      {isLoaded ? t('auth.password') : 'Password'}
                    </Label>
                    <Link
                      href="/auth/forgot-password"
                      className="text-sm font-medium text-primary hover:text-primary/80 transition-colors duration-200"
                    >
                      {isLoaded ? t('auth.forgotPassword') : 'Forgot password?'}
                    </Link>
                  </div>
                  <div className="relative">
                    <motion.button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-4 text-muted-foreground hover:text-white transition-colors duration-200 z-10"
                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </motion.button>
                    <Input
                      enhanced
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoCapitalize="none"
                      autoComplete="current-password"
                      className="pr-12"
                      value={formData.password}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </motion.div>
                <motion.div
                  className="flex items-center space-x-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  <Checkbox
                    id="remember"
                    checked={formData.rememberMe}
                    onCheckedChange={handleCheckboxChange}
                    className="border-primary/30 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                  />
                  <label
                    htmlFor="remember"
                    className="text-sm font-medium leading-none text-white cursor-pointer peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {isLoaded ? t('auth.rememberMe') : 'Remember me'}
                  </label>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <AuthButton
                  className="w-full"
                  onClick={handleSubmit}
                  loading={isLoading}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    isLoaded ? t('auth.signingIn') : 'Signing in...'
                  ) : (
                    isLoaded ? t('auth.accessCourses') : 'Access Courses'
                  )}
                </AuthButton>
              </motion.div>

              <motion.div
                className="mt-6 text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <p className="text-sm text-muted-foreground mb-4">
                  {isLoaded ? t('auth.courseAccess') : 'By logging in, you\'ll get access to:'}
                </p>
                <div className="grid gap-2 text-sm text-muted-foreground">
                  <motion.div
                    className="flex items-center gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.8 }}
                  >
                    <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                    {isLoaded ? t('auth.exclusiveVideos') : 'Exclusive video tutorials'}
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.9 }}
                  >
                    <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                    {isLoaded ? t('auth.liveWebinars') : 'Live webinars and workshops'}
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 1.0 }}
                  >
                    <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                    {isLoaded ? t('auth.certificateCompletion') : 'Certificate of completion'}
                  </motion.div>
                </div>
              </motion.div>

              <motion.div
                className="relative"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.1 }}
              >
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-primary/20"></span>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-black/80 px-3 text-muted-foreground">
                    {isLoaded ? t('auth.orContinueWith') : 'Or continue with'}
                  </span>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.2 }}
              >
                <AuthButton variant="outline" className="w-full">
                  <Github className="mr-2 h-4 w-4" />
                  Github
                </AuthButton>
              </motion.div>
            </div>
          </AuthCardContent>
          <AuthCardFooter>
            <motion.p
              className="text-center text-sm text-muted-foreground"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.3 }}
            >
              {isLoaded ? t('auth.noAccount') : "Don't have an account?"}{' '}
              <Link
                href="/auth/signup"
                className="font-medium text-primary hover:text-primary/80 transition-colors duration-200 underline underline-offset-4"
              >
                {isLoaded ? t('auth.signUp') : 'Sign up'}
              </Link>
            </motion.p>
          </AuthCardFooter>
        </AuthCard>
      </div>
    </AnimatedGradientBackground>
  );
}
