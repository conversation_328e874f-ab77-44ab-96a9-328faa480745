'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { useLanguage } from '@/lib/context/language-context';
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react';

export default function ForgotPasswordPage() {
  const { t, isLoaded } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitted(true);
    }, 1500);
  };

  return (
    <AnimatedGradientBackground className="min-h-screen flex items-center justify-center py-20">
      <div className="w-full max-w-md px-4">
        <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold tracking-tight">
              {isLoaded ? t('auth.resetPassword') : 'Reset your password'}
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              {isSubmitted 
                ? (isLoaded ? t('auth.checkEmail') : 'Check your email for a reset link')
                : (isLoaded ? t('auth.enterEmail') : 'Enter your email to receive a reset link')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isSubmitted ? (
              <div className="flex flex-col items-center justify-center py-4 space-y-4">
                <div className="rounded-full bg-primary/10 p-3">
                  <CheckCircle className="h-6 w-6 text-primary" />
                </div>
                <p className="text-center text-muted-foreground">
                  {isLoaded 
                    ? t('auth.resetLinkSent') 
                    : 'We have sent a password reset link to your email. Please check your inbox.'}
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="email">{isLoaded ? t('auth.email') : 'Email'}</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      placeholder={isLoaded ? t('auth.emailPlaceholder') : '<EMAIL>'}
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      className="pl-10"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </div>
                </div>
                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary/90"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      {isLoaded ? t('auth.sending') : 'Sending...'}
                    </div>
                  ) : (
                    isLoaded ? t('auth.sendResetLink') : 'Send reset link'
                  )}
                </Button>
              </form>
            )}
          </CardContent>
          <CardFooter className="flex flex-col">
            <Link
              href="/auth/login"
              className="inline-flex items-center justify-center text-sm font-medium text-primary hover:text-primary/80"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              {isLoaded ? t('auth.backToLogin') : 'Back to login'}
            </Link>
          </CardFooter>
        </Card>
      </div>
    </AnimatedGradientBackground>
  );
}
