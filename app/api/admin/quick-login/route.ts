import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const { email, name } = await request.json();

    // Check if admin user already exists
    let user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      // Create admin user
      user = await prisma.user.create({
        data: {
          email,
          name,
          role: 'ADMIN'
        }
      });
    } else if (user.role !== 'ADMIN') {
      // Update existing user to admin
      user = await prisma.user.update({
        where: { email },
        data: { role: 'ADMIN' }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Admin user ready',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Quick login error:', error);
    return NextResponse.json(
      { error: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}
