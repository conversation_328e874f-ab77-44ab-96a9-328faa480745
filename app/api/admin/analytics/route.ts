import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import analyticsService from '@/lib/services/analyticsService';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') as '7d' | '30d' | '90d' | '1y' || '30d';
    const type = searchParams.get('type') || 'dashboard';

    let data;

    switch (type) {
      case 'dashboard':
        data = await analyticsService.getDashboardMetrics(timeRange);
        break;
      case 'courses':
        data = await analyticsService.getCourseAnalytics(timeRange);
        break;
      case 'users':
        data = await analyticsService.getUserEngagement(timeRange);
        break;
      case 'revenue':
        data = await analyticsService.getRevenueAnalytics(timeRange);
        break;
      case 'content':
        data = await analyticsService.getContentAnalytics();
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid analytics type' },
          { status: 400 }
        );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, format } = body;

    const exportData = await analyticsService.exportAnalytics(type, format);

    return NextResponse.json({ 
      data: exportData,
      filename: `${type}_analytics_${new Date().toISOString().split('T')[0]}.${format}`
    });
  } catch (error) {
    console.error('Analytics export error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
