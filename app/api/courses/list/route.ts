import { NextRequest, NextResponse } from 'next/server';
import courseService from '@/lib/services/courseService';

export async function GET(request: NextRequest) {
  try {
    const courses = await courseService.getAllCourses();
    
    return NextResponse.json({
      success: true,
      courses: courses.map(course => ({
        id: course.id,
        title: course.title,
        description: course.description,
        instructor: course.instructor,
        price: course.price,
        isPublished: course.isPublished
      }))
    });
  } catch (error) {
    console.error('Failed to list courses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch courses' },
      { status: 500 }
    );
  }
}
