'use client';

import { motion } from 'framer-motion';
import { AnimatedText } from '@/components/ui/animated-text';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';
import { <PERSON>ver<PERSON><PERSON>, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { TeamMemberCard } from '@/components/ui/team-member-card';

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'AI Research Lead',
    image: '/images/Team/team1.jpg',
    bio: '<PERSON> leads our AI research initiatives, bringing over 10 years of experience in machine learning and neural networks. Previously worked at Google AI and has published numerous papers on reinforcement learning.',
    expertise: ['Machine Learning', 'Neural Networks', 'Reinforcement Learning'],
  },
  {
    name: '<PERSON>',
    role: 'ML <PERSON>',
    image: '/images/Team/team2.jpg',
    bio: '<PERSON> specializes in developing and deploying machine learning models at scale. With a background in computer science and statistics, she excels at turning complex algorithms into practical solutions.',
    expertise: ['Ten<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>or<PERSON>', 'M<PERSON><PERSON><PERSON>'],
  },
  {
    name: '<PERSON>',
    role: 'Data Scientist',
    image: '/images/Team/team3.jpg',
    bio: '<PERSON> is an expert in extracting insights from complex datasets. His background in mathematics and statistics enables him to develop sophisticated analytical models for various business applications.',
    expertise: ['Statistical Analysis', 'Data Visualization', 'Predictive Modeling'],
  },
  {
    name: 'Emma Wilson',
    role: 'MLOps Specialist',
    image: '/images/Team/team4.jpg',
    bio: 'Emma ensures our AI solutions are deployed efficiently and reliably. She bridges the gap between development and operations, implementing best practices for continuous integration and deployment of ML models.',
    expertise: ['CI/CD Pipelines', 'Kubernetes', 'Docker'],
  },
  {
    name: 'David Kim',
    role: 'NLP Engineer',
    image: '/images/Team/team5.jpg',
    bio: 'David focuses on natural language processing applications, developing solutions for text analysis, sentiment analysis, and conversational AI. He has contributed to several open-source NLP libraries.',
    expertise: ['BERT', 'GPT', 'Transformer Models'],
  },
  {
    name: 'Olivia Martinez',
    role: 'Computer Vision Specialist',
    image: '/images/Team/team6.jpg',
    bio: 'Olivia specializes in computer vision algorithms and applications. Her work enables machines to interpret and understand visual information from the world, with applications in object detection and image recognition.',
    expertise: ['OpenCV', 'Image Processing', 'Object Detection'],
  },
  {
    name: 'James Wilson',
    role: 'AI Solutions Architect',
    image: '/images/Team/team7.jpg',
    bio: 'James designs comprehensive AI solutions that address complex business challenges. He works closely with clients to understand their needs and translate them into technical requirements.',
    expertise: ['System Design', 'Cloud Architecture', 'Solution Engineering'],
  },
  {
    name: 'Sophia Lee',
    role: 'AI Ethics Researcher',
    image: '/images/Team/team1.jpg', // Using team1 again as we only have 7 images
    bio: 'Sophia ensures our AI solutions are developed and deployed ethically. She focuses on fairness, accountability, transparency, and privacy in AI systems, guiding our approach to responsible AI.',
    expertise: ['Ethical AI', 'Bias Mitigation', 'AI Governance'],
  },
];

export default function TeamPage() {
  return (
    <>
      <AnimatedGradientBackground className="min-h-[40vh] flex flex-col items-center justify-center pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-4"
            >
              Our Team
            </motion.div>

            <AnimatedText
              text="Meet Our AI Experts"
              className="text-4xl md:text-5xl font-bold tracking-tight mb-6"
            />

            <AnimatedText
              text="A collective of talented professionals dedicated to innovation and excellence in artificial intelligence."
              className="text-xl text-muted-foreground"
              once
            />
          </div>
        </div>
      </AnimatedGradientBackground>

      <section className="py-20 bg-black">
        <div className="w-full px-4">
          <TextReveal className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-4 text-white">
              The Minds Behind Our Innovation
            </h2>
            <p className="text-muted-foreground">
              Our diverse team of AI specialists brings together expertise from various domains to deliver comprehensive solutions for your business.
            </p>
          </TextReveal>

          {/* New Team Grid Layout - Full Width with optimizations */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mt-12 w-full px-2 will-change-transform">
            {teamMembers.map((member, index) => (
              <div
                key={`team-page-${member.name}`}
                className="will-change-transform"
                style={{
                  containIntrinsicSize: '0 360px', // Hint for browser about size
                  contentVisibility: 'auto' // Improve rendering performance
                }}
              >
                <TeamMemberCard
                  key={`team-card-${member.name}`}
                  name={member.name}
                  role={member.role}
                  image={member.image}
                  index={index}
                  className="h-[280px] md:h-[320px] lg:h-[360px]" // Reduced size by ~20%
                />
              </div>
            ))}
          </div>

          {/* Team Member Details Section */}
          <div className="mt-20">
            <TextReveal className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="text-2xl font-bold mb-4 text-white">
                Team Expertise
              </h2>
              <p className="text-muted-foreground">
                Learn more about our team members' skills and experience
              </p>
            </TextReveal>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {teamMembers.map((member) => (
                <HoverCard key={member.name}>
                  <HoverCardTrigger asChild>
                    <div className="p-4 border border-white/10 rounded-lg bg-black/50 backdrop-blur-sm cursor-pointer hover:border-primary/30 transition-all duration-300">
                      <h3 className="font-medium text-lg text-white">{member.name}</h3>
                      <p className="text-sm text-primary">{member.role}</p>
                    </div>
                  </HoverCardTrigger>
                  <HoverCardContent className="w-80 bg-black/80 backdrop-blur-xl border-white/10">
                    <div className="space-y-4">
                      <div className="flex justify-between space-x-4">
                        <div className="relative h-12 w-12 rounded-full bg-primary/20">
                          <div className="absolute inset-0 flex items-center justify-center text-primary">
                            {member.name.charAt(0)}
                          </div>
                        </div>
                        <div className="space-y-1">
                          <h4 className="text-sm font-semibold text-white">{member.name}</h4>
                          <p className="text-sm text-primary">{member.role}</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm text-white/80">{member.bio}</p>
                        <div className="flex flex-wrap gap-1">
                          {member.expertise.map((skill) => (
                            <span
                              key={skill}
                              className="inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-semibold transition-colors border-transparent bg-primary/10 text-primary"
                            >
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
              ))}
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
