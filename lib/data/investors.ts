// Investor companies data
export const investorCompanies = [
  {
    id: 1,
    name: 'Venture Capital Partners',
    logo: '/images/investors/1.png',
    description: 'Leading venture capital firm focused on early-stage technology startups.',
    investmentFocus: ['Technology', 'AI', 'Fintech'],
    website: 'https://example.com',
    featured: true,
  },
  {
    id: 2,
    name: 'Growth Equity Fund',
    logo: '/images/investors/2.png',
    description: 'Growth equity investors supporting companies with proven business models.',
    investmentFocus: ['SaaS', 'Healthcare', 'E-commerce'],
    website: 'https://example.com',
    featured: true,
  },
  {
    id: 3,
    name: 'Innovation Capital',
    logo: '/images/investors/3.png',
    description: 'Seed-stage investors focused on disruptive technologies and innovative business models.',
    investmentFocus: ['Deep Tech', 'Blockchain', 'Sustainability'],
    website: 'https://example.com',
    featured: false,
  },
  {
    id: 4,
    name: 'Strategic Ventures',
    logo: '/images/investors/4.png',
    description: 'Corporate venture arm providing strategic capital and industry expertise.',
    investmentFocus: ['Enterprise Software', 'IoT', 'Digital Transformation'],
    website: 'https://example.com',
    featured: false,
  },
  {
    id: 5,
    name: 'Global Investments',
    logo: '/images/investors/5.png',
    description: 'International investment firm with a focus on cross-border expansion and global markets.',
    investmentFocus: ['International Expansion', 'Emerging Markets', 'Consumer Tech'],
    website: 'https://example.com',
    featured: false,
  },
];

// Investor persons data
export const investorPersons = [
  {
    id: 1,
    name: 'Alexandra Chen',
    role: 'Managing Partner',
    company: 'Venture Capital Partners',
    image: '/images/investors/1.png',
    bio: 'Experienced investor with a track record of successful exits in the technology sector.',
    expertise: ['Early-stage Investments', 'Technology', 'Board Governance'],
    featured: true,
  },
  {
    id: 2,
    name: 'Michael Rodriguez',
    role: 'Investment Director',
    company: 'Growth Equity Fund',
    image: '/images/investors/2.png',
    bio: 'Former entrepreneur turned investor with deep expertise in scaling SaaS businesses.',
    expertise: ['Growth Equity', 'SaaS', 'M&A'],
    featured: true,
  },
  {
    id: 3,
    name: 'Sarah Johnson',
    role: 'Principal',
    company: 'Innovation Capital',
    image: '/images/investors/3.png',
    bio: 'Specializes in identifying disruptive technologies with significant market potential.',
    expertise: ['Deep Tech', 'Due Diligence', 'Startup Mentoring'],
    featured: false,
  },
  {
    id: 4,
    name: 'David Park',
    role: 'Venture Partner',
    company: 'Strategic Ventures',
    image: '/images/investors/4.png',
    bio: 'Corporate innovation expert helping startups navigate enterprise partnerships.',
    expertise: ['Corporate Venturing', 'Enterprise Sales', 'Strategic Partnerships'],
    featured: false,
  },
  {
    id: 5,
    name: 'Elena Vasquez',
    role: 'General Partner',
    company: 'Global Investments',
    image: '/images/investors/5.png',
    bio: 'International investor with extensive experience in emerging markets and cross-border deals.',
    expertise: ['International Expansion', 'Emerging Markets', 'Cross-border Investments'],
    featured: false,
  },
];

// Partners data
export const partners = [
  {
    id: 1,
    name: 'TechHub Accelerator',
    logo: '/images/investors/1.png', // Using investor images as placeholders
    description: 'Leading technology accelerator providing mentorship and resources to early-stage startups.',
    partnershipType: 'Accelerator',
    website: 'https://example.com',
  },
  {
    id: 2,
    name: 'Global University',
    logo: '/images/investors/2.png',
    description: 'Academic partner offering research collaboration and talent pipeline.',
    partnershipType: 'Academic',
    website: 'https://example.com',
  },
  {
    id: 3,
    name: 'Enterprise Solutions',
    logo: '/images/investors/3.png',
    description: 'Corporate partner providing industry expertise and customer connections.',
    partnershipType: 'Corporate',
    website: 'https://example.com',
  },
  {
    id: 4,
    name: 'Government Innovation Agency',
    logo: '/images/investors/4.png',
    description: 'Public sector partner supporting innovation through grants and policy initiatives.',
    partnershipType: 'Government',
    website: 'https://example.com',
  },
  {
    id: 5,
    name: 'Startup Association',
    logo: '/images/investors/5.png',
    description: 'Industry association connecting startups with resources, events, and networking opportunities.',
    partnershipType: 'Industry',
    website: 'https://example.com',
  },
];
