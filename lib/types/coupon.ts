// Coupon system types and interfaces for InnoHub courses platform

export type DiscountType = 'percentage' | 'free_access';
export type CouponScope = 'course_specific' | 'platform_wide';
export type CouponStatus = 'active' | 'inactive' | 'expired';
export type UsageType = 'single_use' | 'limited_uses' | 'unlimited';

export interface Coupon {
  id: string;
  code: string; // Unique alphanumeric code (e.g., "INNOHUB2024", "STARTUP50")
  name: string; // Human-readable name for admin purposes
  description?: string;
  
  // Discount configuration
  discountType: DiscountType;
  discountValue: number; // Percentage (0-100) or 0 for free access
  
  // Scope and applicability
  scope: CouponScope;
  applicableCourseIds?: string[]; // Only for course_specific coupons
  
  // Usage limits
  usageType: UsageType;
  maxUses?: number; // Only for limited_uses
  currentUses: number;
  
  // Validity period
  startDate: Date;
  expirationDate?: Date;
  
  // Status and metadata
  status: CouponStatus;
  createdAt: Date;
  createdBy: string; // Admin user ID
  lastModified: Date;
  
  // Advanced features
  isWelcomeCoupon?: boolean;
  isReferralCoupon?: boolean;
  referralUserId?: string; // For referral tracking
  minimumCoursePrice?: number; // Minimum course price to apply coupon
  
  // Analytics
  conversionRate?: number;
  totalRevenueSaved?: number;
}

export interface CouponRedemption {
  id: string;
  couponId: string;
  couponCode: string;
  userId: string;
  courseId?: string; // null for platform-wide coupons
  
  // Redemption details
  originalPrice: number;
  discountAmount: number;
  finalPrice: number;
  
  // Timestamps
  redeemedAt: Date;
  expiresAt?: Date; // When the coupon access expires
  
  // Status
  isActive: boolean;
  isUsed: boolean;
}

export interface UserCouponStatus {
  userId: string;
  availableCoupons: Coupon[];
  appliedCoupons: CouponRedemption[];
  couponHistory: CouponRedemption[];
  totalSavings: number;
}

export interface CouponValidationResult {
  isValid: boolean;
  coupon?: Coupon;
  error?: CouponValidationError;
  discountAmount?: number;
  finalPrice?: number;
}

export type CouponValidationError = 
  | 'COUPON_NOT_FOUND'
  | 'COUPON_EXPIRED'
  | 'COUPON_INACTIVE'
  | 'COUPON_ALREADY_USED'
  | 'COUPON_USAGE_LIMIT_REACHED'
  | 'COUPON_NOT_APPLICABLE_TO_COURSE'
  | 'COUPON_MINIMUM_PRICE_NOT_MET'
  | 'USER_NOT_ELIGIBLE';

export interface CouponApplicationRequest {
  couponCode: string;
  userId: string;
  courseId?: string;
  coursePrice?: number;
}

export interface CouponAnalytics {
  couponId: string;
  totalRedemptions: number;
  uniqueUsers: number;
  conversionRate: number;
  totalRevenueSaved: number;
  averageDiscountAmount: number;
  redemptionsByDate: { date: string; count: number }[];
  topCourses: { courseId: string; courseName: string; redemptions: number }[];
}

export interface CouponGenerationOptions {
  prefix?: string;
  length?: number;
  includeNumbers?: boolean;
  includeLetters?: boolean;
  excludeSimilarChars?: boolean; // Exclude 0, O, I, l, etc.
}

// Predefined coupon templates for common use cases
export interface CouponTemplate {
  id: string;
  name: string;
  description: string;
  defaultValues: Partial<Coupon>;
}

export const COUPON_TEMPLATES: CouponTemplate[] = [
  {
    id: 'welcome_new_user',
    name: 'Welcome New User',
    description: 'Free access coupon for new users',
    defaultValues: {
      discountType: 'free_access',
      discountValue: 100,
      scope: 'platform_wide',
      usageType: 'single_use',
      isWelcomeCoupon: true,
    }
  },
  {
    id: 'seasonal_discount',
    name: 'Seasonal Discount',
    description: '50% off seasonal promotion',
    defaultValues: {
      discountType: 'percentage',
      discountValue: 50,
      scope: 'platform_wide',
      usageType: 'unlimited',
    }
  },
  {
    id: 'course_specific_free',
    name: 'Course Specific Free Access',
    description: 'Free access to specific course',
    defaultValues: {
      discountType: 'free_access',
      discountValue: 100,
      scope: 'course_specific',
      usageType: 'unlimited',
    }
  },
  {
    id: 'referral_bonus',
    name: 'Referral Bonus',
    description: 'Referral reward coupon',
    defaultValues: {
      discountType: 'percentage',
      discountValue: 25,
      scope: 'platform_wide',
      usageType: 'single_use',
      isReferralCoupon: true,
    }
  }
];

// Error messages for user-facing validation
export const COUPON_ERROR_MESSAGES = {
  COUPON_NOT_FOUND: 'Coupon code not found. Please check the code and try again.',
  COUPON_EXPIRED: 'This coupon has expired and can no longer be used.',
  COUPON_INACTIVE: 'This coupon is currently inactive.',
  COUPON_ALREADY_USED: 'You have already used this coupon.',
  COUPON_USAGE_LIMIT_REACHED: 'This coupon has reached its usage limit.',
  COUPON_NOT_APPLICABLE_TO_COURSE: 'This coupon is not applicable to the selected course.',
  COUPON_MINIMUM_PRICE_NOT_MET: 'Course price does not meet the minimum requirement for this coupon.',
  USER_NOT_ELIGIBLE: 'You are not eligible to use this coupon.',
} as const;
