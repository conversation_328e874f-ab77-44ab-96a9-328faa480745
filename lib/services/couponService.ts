import { 
  Coupon, 
  CouponRedemption, 
  CouponValidationResult, 
  CouponApplicationRequest,
  CouponValidationError,
  UserCouponStatus,
  CouponGenerationOptions,
  COUPON_ERROR_MESSAGES
} from '@/lib/types/coupon';

// Mock storage - In production, this would be replaced with database calls
class CouponStorage {
  private coupons: Map<string, Coupon> = new Map();
  private redemptions: Map<string, CouponRedemption> = new Map();
  private userRedemptions: Map<string, string[]> = new Map(); // userId -> redemptionIds

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData() {
    // Create some sample coupons for testing
    const sampleCoupons: Coupon[] = [
      {
        id: '1',
        code: 'WELCOME2024',
        name: 'Welcome New User',
        description: 'Free access to any course for new users',
        discountType: 'free_access',
        discountValue: 100,
        scope: 'platform_wide',
        usageType: 'single_use',
        currentUses: 0,
        startDate: new Date('2024-01-01'),
        expirationDate: new Date('2024-12-31'),
        status: 'active',
        createdAt: new Date(),
        createdBy: 'admin',
        lastModified: new Date(),
        isWelcomeCoupon: true,
      },
      {
        id: '2',
        code: 'STARTUP50',
        name: 'Startup Discount',
        description: '50% off all courses',
        discountType: 'percentage',
        discountValue: 50,
        scope: 'platform_wide',
        usageType: 'unlimited',
        currentUses: 0,
        startDate: new Date('2024-01-01'),
        expirationDate: new Date('2024-06-30'),
        status: 'active',
        createdAt: new Date(),
        createdBy: 'admin',
        lastModified: new Date(),
      },
      {
        id: '3',
        code: 'ENTREPRENEUR',
        name: 'Entrepreneurship Course Free',
        description: 'Free access to Entrepreneurship Fundamentals',
        discountType: 'free_access',
        discountValue: 100,
        scope: 'course_specific',
        applicableCourseIds: ['1'], // Entrepreneurship Fundamentals
        usageType: 'unlimited',
        currentUses: 0,
        startDate: new Date('2024-01-01'),
        status: 'active',
        createdAt: new Date(),
        createdBy: 'admin',
        lastModified: new Date(),
      }
    ];

    sampleCoupons.forEach(coupon => {
      this.coupons.set(coupon.code.toLowerCase(), coupon);
    });
  }

  getCouponByCode(code: string): Coupon | undefined {
    return this.coupons.get(code.toLowerCase());
  }

  saveCoupon(coupon: Coupon): void {
    this.coupons.set(coupon.code.toLowerCase(), coupon);
  }

  saveRedemption(redemption: CouponRedemption): void {
    this.redemptions.set(redemption.id, redemption);
    
    // Update user redemptions index
    const userRedemptions = this.userRedemptions.get(redemption.userId) || [];
    userRedemptions.push(redemption.id);
    this.userRedemptions.set(redemption.userId, userRedemptions);
  }

  getUserRedemptions(userId: string): CouponRedemption[] {
    const redemptionIds = this.userRedemptions.get(userId) || [];
    return redemptionIds.map(id => this.redemptions.get(id)).filter(Boolean) as CouponRedemption[];
  }

  getAllCoupons(): Coupon[] {
    return Array.from(this.coupons.values());
  }
}

class CouponService {
  private storage = new CouponStorage();

  /**
   * Generate a unique coupon code
   */
  generateCouponCode(options: CouponGenerationOptions = {}): string {
    const {
      prefix = '',
      length = 8,
      includeNumbers = true,
      includeLetters = true,
      excludeSimilarChars = true
    } = options;

    let chars = '';
    if (includeLetters) {
      chars += excludeSimilarChars ? 'ABCDEFGHJKMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    }
    if (includeNumbers) {
      chars += excludeSimilarChars ? '23456789' : '0123456789';
    }

    let result = prefix;
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // Ensure uniqueness
    if (this.storage.getCouponByCode(result)) {
      return this.generateCouponCode(options);
    }

    return result;
  }

  /**
   * Create a new coupon
   */
  createCoupon(couponData: Omit<Coupon, 'id' | 'createdAt' | 'lastModified' | 'currentUses'>): Coupon {
    const coupon: Coupon = {
      ...couponData,
      id: Date.now().toString(),
      createdAt: new Date(),
      lastModified: new Date(),
      currentUses: 0,
    };

    this.storage.saveCoupon(coupon);
    return coupon;
  }

  /**
   * Validate a coupon for a specific application
   */
  async validateCoupon(request: CouponApplicationRequest): Promise<CouponValidationResult> {
    const { couponCode, userId, courseId, coursePrice = 0 } = request;

    // Find the coupon
    const coupon = this.storage.getCouponByCode(couponCode);
    if (!coupon) {
      return {
        isValid: false,
        error: 'COUPON_NOT_FOUND'
      };
    }

    // Check if coupon is active
    if (coupon.status !== 'active') {
      return {
        isValid: false,
        error: 'COUPON_INACTIVE'
      };
    }

    // Check expiration
    if (coupon.expirationDate && new Date() > coupon.expirationDate) {
      return {
        isValid: false,
        error: 'COUPON_EXPIRED'
      };
    }

    // Check usage limits
    if (coupon.usageType === 'limited_uses' && coupon.maxUses && coupon.currentUses >= coupon.maxUses) {
      return {
        isValid: false,
        error: 'COUPON_USAGE_LIMIT_REACHED'
      };
    }

    // Check if user has already used this coupon (for single-use coupons)
    if (coupon.usageType === 'single_use') {
      const userRedemptions = this.storage.getUserRedemptions(userId);
      const hasUsedCoupon = userRedemptions.some(redemption => 
        redemption.couponCode.toLowerCase() === couponCode.toLowerCase()
      );
      
      if (hasUsedCoupon) {
        return {
          isValid: false,
          error: 'COUPON_ALREADY_USED'
        };
      }
    }

    // Check course-specific applicability
    if (coupon.scope === 'course_specific' && courseId) {
      if (!coupon.applicableCourseIds?.includes(courseId)) {
        return {
          isValid: false,
          error: 'COUPON_NOT_APPLICABLE_TO_COURSE'
        };
      }
    }

    // Check minimum price requirement
    if (coupon.minimumCoursePrice && coursePrice < coupon.minimumCoursePrice) {
      return {
        isValid: false,
        error: 'COUPON_MINIMUM_PRICE_NOT_MET'
      };
    }

    // Calculate discount
    const discountAmount = coupon.discountType === 'free_access' 
      ? coursePrice 
      : Math.round((coursePrice * coupon.discountValue) / 100);
    
    const finalPrice = Math.max(0, coursePrice - discountAmount);

    return {
      isValid: true,
      coupon,
      discountAmount,
      finalPrice
    };
  }

  /**
   * Apply a coupon and create a redemption record
   */
  async applyCoupon(request: CouponApplicationRequest): Promise<CouponRedemption | null> {
    const validation = await this.validateCoupon(request);
    
    if (!validation.isValid || !validation.coupon) {
      throw new Error(COUPON_ERROR_MESSAGES[validation.error as CouponValidationError]);
    }

    const { coupon, discountAmount = 0, finalPrice = 0 } = validation;
    const { userId, courseId, coursePrice = 0 } = request;

    // Create redemption record
    const redemption: CouponRedemption = {
      id: Date.now().toString(),
      couponId: coupon.id,
      couponCode: coupon.code,
      userId,
      courseId,
      originalPrice: coursePrice,
      discountAmount,
      finalPrice,
      redeemedAt: new Date(),
      isActive: true,
      isUsed: true,
    };

    // Update coupon usage count
    coupon.currentUses += 1;
    coupon.lastModified = new Date();
    this.storage.saveCoupon(coupon);

    // Save redemption
    this.storage.saveRedemption(redemption);

    return redemption;
  }

  /**
   * Get user's coupon status including available and applied coupons
   */
  async getUserCouponStatus(userId: string): Promise<UserCouponStatus> {
    const userRedemptions = this.storage.getUserRedemptions(userId);
    const allCoupons = this.storage.getAllCoupons();
    
    // Filter available coupons (active, not expired, not used by user if single-use)
    const availableCoupons = allCoupons.filter(coupon => {
      if (coupon.status !== 'active') return false;
      if (coupon.expirationDate && new Date() > coupon.expirationDate) return false;
      
      if (coupon.usageType === 'single_use') {
        const hasUsed = userRedemptions.some(redemption => 
          redemption.couponCode.toLowerCase() === coupon.code.toLowerCase()
        );
        if (hasUsed) return false;
      }
      
      return true;
    });

    const appliedCoupons = userRedemptions.filter(redemption => redemption.isActive);
    const totalSavings = userRedemptions.reduce((sum, redemption) => sum + redemption.discountAmount, 0);

    return {
      userId,
      availableCoupons,
      appliedCoupons,
      couponHistory: userRedemptions,
      totalSavings,
    };
  }

  /**
   * Get all coupons (admin function)
   */
  getAllCoupons(): Coupon[] {
    return this.storage.getAllCoupons();
  }

  /**
   * Update coupon status
   */
  updateCouponStatus(couponId: string, status: 'active' | 'inactive'): boolean {
    const allCoupons = this.storage.getAllCoupons();
    const coupon = allCoupons.find(c => c.id === couponId);
    
    if (coupon) {
      coupon.status = status;
      coupon.lastModified = new Date();
      this.storage.saveCoupon(coupon);
      return true;
    }
    
    return false;
  }
}

// Export singleton instance
export const couponService = new CouponService();
export default couponService;
