import couponService from './couponService';
import { CouponRedemption } from '@/lib/types/coupon';

export interface CourseAccess {
  courseId: string;
  hasAccess: boolean;
  accessType: 'free' | 'premium' | 'coupon_unlocked';
  originalPrice?: number;
  currentPrice?: number;
  appliedCoupon?: CouponRedemption;
  unlockMethod?: string;
}

export interface UserCourseAccess {
  userId: string;
  courseAccess: Map<string, CourseAccess>;
  totalCoursesUnlocked: number;
  totalSavings: number;
}

class CourseAccessService {
  private userAccess: Map<string, UserCourseAccess> = new Map();

  /**
   * Check if user has access to a specific course
   */
  async checkCourseAccess(userId: string, courseId: string): Promise<CourseAccess> {
    // Get user's coupon redemptions
    const couponStatus = await couponService.getUserCouponStatus(userId);
    
    // Check if user has a coupon redemption for this course
    const courseRedemption = couponStatus.appliedCoupons.find(
      redemption => redemption.courseId === courseId || !redemption.courseId // platform-wide coupons
    );

    // Default course pricing (in a real app, this would come from a course service)
    const coursePrice = this.getDefaultCoursePrice(courseId);

    if (courseRedemption) {
      return {
        courseId,
        hasAccess: true,
        accessType: 'coupon_unlocked',
        originalPrice: courseRedemption.originalPrice,
        currentPrice: courseRedemption.finalPrice,
        appliedCoupon: courseRedemption,
        unlockMethod: `Unlocked with coupon: ${courseRedemption.couponCode}`
      };
    }

    // Check for platform-wide coupons that could apply
    const platformWideCoupon = couponStatus.appliedCoupons.find(
      redemption => !redemption.courseId
    );

    if (platformWideCoupon) {
      return {
        courseId,
        hasAccess: true,
        accessType: 'coupon_unlocked',
        originalPrice: coursePrice,
        currentPrice: 0, // Platform-wide coupons typically give free access
        appliedCoupon: platformWideCoupon,
        unlockMethod: `Unlocked with platform coupon: ${platformWideCoupon.couponCode}`
      };
    }

    // Default access (would integrate with payment system in production)
    return {
      courseId,
      hasAccess: false,
      accessType: 'premium',
      originalPrice: coursePrice,
      currentPrice: coursePrice,
    };
  }

  /**
   * Get all course access for a user
   */
  async getUserCourseAccess(userId: string, courseIds: string[]): Promise<UserCourseAccess> {
    const courseAccess = new Map<string, CourseAccess>();
    let totalSavings = 0;
    let totalCoursesUnlocked = 0;

    for (const courseId of courseIds) {
      const access = await this.checkCourseAccess(userId, courseId);
      courseAccess.set(courseId, access);

      if (access.hasAccess && access.accessType === 'coupon_unlocked') {
        totalCoursesUnlocked++;
        if (access.originalPrice && access.currentPrice !== undefined) {
          totalSavings += (access.originalPrice - access.currentPrice);
        }
      }
    }

    const userAccess: UserCourseAccess = {
      userId,
      courseAccess,
      totalCoursesUnlocked,
      totalSavings,
    };

    this.userAccess.set(userId, userAccess);
    return userAccess;
  }

  /**
   * Apply coupon to unlock course access
   */
  async applyCouponToCourse(
    userId: string, 
    courseId: string, 
    couponCode: string
  ): Promise<CourseAccess> {
    const coursePrice = this.getDefaultCoursePrice(courseId);

    try {
      // Apply the coupon through the coupon service
      const redemption = await couponService.applyCoupon({
        couponCode,
        userId,
        courseId,
        coursePrice,
      });

      if (redemption) {
        // Update user's course access
        const access: CourseAccess = {
          courseId,
          hasAccess: true,
          accessType: 'coupon_unlocked',
          originalPrice: redemption.originalPrice,
          currentPrice: redemption.finalPrice,
          appliedCoupon: redemption,
          unlockMethod: `Unlocked with coupon: ${redemption.couponCode}`
        };

        // Update cached access
        const userAccess = this.userAccess.get(userId);
        if (userAccess) {
          userAccess.courseAccess.set(courseId, access);
          userAccess.totalCoursesUnlocked++;
          userAccess.totalSavings += redemption.discountAmount;
        }

        return access;
      }
    } catch (error) {
      console.error('Failed to apply coupon to course:', error);
      throw error;
    }

    // Return current access if coupon application failed
    return this.checkCourseAccess(userId, courseId);
  }

  /**
   * Get courses unlocked by coupons for dashboard display
   */
  async getCouponUnlockedCourses(userId: string): Promise<CourseAccess[]> {
    const couponStatus = await couponService.getUserCouponStatus(userId);
    const unlockedCourses: CourseAccess[] = [];

    for (const redemption of couponStatus.appliedCoupons) {
      if (redemption.isActive) {
        const access: CourseAccess = {
          courseId: redemption.courseId || 'platform_wide',
          hasAccess: true,
          accessType: 'coupon_unlocked',
          originalPrice: redemption.originalPrice,
          currentPrice: redemption.finalPrice,
          appliedCoupon: redemption,
          unlockMethod: `Unlocked with coupon: ${redemption.couponCode}`
        };
        unlockedCourses.push(access);
      }
    }

    return unlockedCourses;
  }

  /**
   * Check if user can access course content (for video playback, etc.)
   */
  async canAccessCourseContent(userId: string, courseId: string): Promise<boolean> {
    const access = await this.checkCourseAccess(userId, courseId);
    return access.hasAccess;
  }

  /**
   * Get course pricing with any applicable coupons
   */
  async getCoursePrice(userId: string, courseId: string): Promise<{
    originalPrice: number;
    currentPrice: number;
    hasDiscount: boolean;
    appliedCoupon?: CouponRedemption;
  }> {
    const access = await this.checkCourseAccess(userId, courseId);
    
    return {
      originalPrice: access.originalPrice || 0,
      currentPrice: access.currentPrice || 0,
      hasDiscount: (access.originalPrice || 0) > (access.currentPrice || 0),
      appliedCoupon: access.appliedCoupon,
    };
  }

  /**
   * Get default course price (mock data - would come from course service in production)
   */
  private getDefaultCoursePrice(courseId: string): number {
    // Mock pricing data
    const coursePrices: { [key: string]: number } = {
      '1': 99, // Entrepreneurship Fundamentals
      '2': 79, // Innovation Hub Insights
      '3': 149, // Startup Funding Masterclass
      '4': 129, // Digital Marketing for Startups
      '5': 199, // Product Development & Design
      '6': 159, // Building Successful Teams
      '7': 119, // Legal Basics for Startups
      '8': 179, // Scaling Your Business
      '9': 139, // Financial Management
      '10': 169, // Innovation Methodologies
    };

    return coursePrices[courseId] || 99;
  }

  /**
   * Get user's course access summary for dashboard
   */
  async getUserAccessSummary(userId: string): Promise<{
    totalCourses: number;
    unlockedCourses: number;
    totalSavings: number;
    activeCoupons: number;
  }> {
    const couponStatus = await couponService.getUserCouponStatus(userId);
    const unlockedCourses = await this.getCouponUnlockedCourses(userId);

    return {
      totalCourses: 10, // Total available courses
      unlockedCourses: unlockedCourses.length,
      totalSavings: couponStatus.totalSavings,
      activeCoupons: couponStatus.appliedCoupons.length,
    };
  }

  /**
   * Remove coupon access (for testing or admin purposes)
   */
  async removeCouponAccess(userId: string, courseId: string, couponCode: string): Promise<boolean> {
    // In a real implementation, this would deactivate the redemption
    // For now, we'll just update the cached access
    const userAccess = this.userAccess.get(userId);
    if (userAccess) {
      const access = userAccess.courseAccess.get(courseId);
      if (access && access.appliedCoupon?.couponCode === couponCode) {
        // Reset to default access
        const defaultAccess = await this.checkCourseAccess(userId, courseId);
        userAccess.courseAccess.set(courseId, defaultAccess);
        return true;
      }
    }
    return false;
  }
}

// Export singleton instance
export const courseAccessService = new CourseAccessService();
export default courseAccessService;
