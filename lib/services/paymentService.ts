import { prisma } from '@/lib/prisma';

export type PaymentMethod = 
  | 'qpay' 
  | 'socialpay' 
  | 'monpay' 
  | 'khan_bank' 
  | 'tdb_bank' 
  | 'golomt_bank'
  | 'card';

export type PaymentStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled' 
  | 'refunded';

export interface PaymentRequest {
  userId: string;
  courseId: string;
  amount: number;
  currency: 'MNT' | 'USD';
  paymentMethod: PaymentMethod;
  couponCode?: string;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  redirectUrl?: string;
  qrCode?: string;
  error?: string;
  transactionId?: string;
}

export interface MongolianBankConfig {
  khanBank: {
    merchantId: string;
    secretKey: string;
    apiUrl: string;
  };
  tdbBank: {
    merchantId: string;
    secretKey: string;
    apiUrl: string;
  };
  golomtBank: {
    merchantId: string;
    secretKey: string;
    apiUrl: string;
  };
}

export interface MobilePaymentConfig {
  qpay: {
    username: string;
    password: string;
    invoiceCode: string;
    apiUrl: string;
  };
  socialPay: {
    merchantId: string;
    secretKey: string;
    apiUrl: string;
  };
  monpay: {
    merchantId: string;
    secretKey: string;
    apiUrl: string;
  };
}

class PaymentService {
  private bankConfig: MongolianBankConfig;
  private mobileConfig: MobilePaymentConfig;

  constructor() {
    // Initialize with environment variables
    this.bankConfig = {
      khanBank: {
        merchantId: process.env.KHAN_BANK_MERCHANT_ID || '',
        secretKey: process.env.KHAN_BANK_SECRET_KEY || '',
        apiUrl: process.env.KHAN_BANK_API_URL || 'https://ecommerce.khanbank.com/api'
      },
      tdbBank: {
        merchantId: process.env.TDB_BANK_MERCHANT_ID || '',
        secretKey: process.env.TDB_BANK_SECRET_KEY || '',
        apiUrl: process.env.TDB_BANK_API_URL || 'https://ecommerce.tdbm.mn/api'
      },
      golomtBank: {
        merchantId: process.env.GOLOMT_BANK_MERCHANT_ID || '',
        secretKey: process.env.GOLOMT_BANK_SECRET_KEY || '',
        apiUrl: process.env.GOLOMT_BANK_API_URL || 'https://ecommerce.golomtbank.com/api'
      }
    };

    this.mobileConfig = {
      qpay: {
        username: process.env.QPAY_USERNAME || '',
        password: process.env.QPAY_PASSWORD || '',
        invoiceCode: process.env.QPAY_INVOICE_CODE || '',
        apiUrl: process.env.QPAY_API_URL || 'https://merchant.qpay.mn/v2'
      },
      socialPay: {
        merchantId: process.env.SOCIALPAY_MERCHANT_ID || '',
        secretKey: process.env.SOCIALPAY_SECRET_KEY || '',
        apiUrl: process.env.SOCIALPAY_API_URL || 'https://api.socialpay.mn'
      },
      monpay: {
        merchantId: process.env.MONPAY_MERCHANT_ID || '',
        secretKey: process.env.MONPAY_SECRET_KEY || '',
        apiUrl: process.env.MONPAY_API_URL || 'https://api.monpay.mn'
      }
    };
  }

  /**
   * Process payment based on selected method
   */
  async processPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // Create payment record
      const payment = await this.createPaymentRecord(request);
      
      switch (request.paymentMethod) {
        case 'qpay':
          return await this.processQPayPayment(payment, request);
        case 'socialpay':
          return await this.processSocialPayPayment(payment, request);
        case 'monpay':
          return await this.processMonPayPayment(payment, request);
        case 'khan_bank':
          return await this.processKhanBankPayment(payment, request);
        case 'tdb_bank':
          return await this.processTDBBankPayment(payment, request);
        case 'golomt_bank':
          return await this.processGolomtBankPayment(payment, request);
        case 'card':
          return await this.processCardPayment(payment, request);
        default:
          throw new Error('Unsupported payment method');
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment processing failed'
      };
    }
  }

  /**
   * QPay integration
   */
  private async processQPayPayment(payment: any, request: PaymentRequest): Promise<PaymentResult> {
    try {
      const { qpay } = this.mobileConfig;
      
      // Get QPay access token
      const tokenResponse = await fetch(`${qpay.apiUrl}/auth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: qpay.username,
          password: qpay.password
        })
      });

      const tokenData = await tokenResponse.json();
      if (!tokenData.access_token) {
        throw new Error('Failed to get QPay access token');
      }

      // Create QPay invoice
      const invoiceResponse = await fetch(`${qpay.apiUrl}/invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokenData.access_token}`
        },
        body: JSON.stringify({
          invoice_code: qpay.invoiceCode,
          sender_invoice_no: payment.id,
          invoice_receiver_code: request.userId,
          invoice_description: `InnoHub Course Payment - ${payment.id}`,
          amount: request.amount,
          callback_url: `${process.env.NEXTAUTH_URL}/api/payments/qpay/callback`
        })
      });

      const invoiceData = await invoiceResponse.json();
      
      if (invoiceData.invoice_id) {
        await this.updatePaymentStatus(payment.id, 'pending', invoiceData.invoice_id);
        
        return {
          success: true,
          paymentId: payment.id,
          qrCode: invoiceData.qr_text,
          redirectUrl: invoiceData.qpay_shortlink
        };
      } else {
        throw new Error('Failed to create QPay invoice');
      }
    } catch (error) {
      await this.updatePaymentStatus(payment.id, 'failed');
      throw error;
    }
  }

  /**
   * SocialPay integration
   */
  private async processSocialPayPayment(payment: any, request: PaymentRequest): Promise<PaymentResult> {
    try {
      const { socialPay } = this.mobileConfig;
      
      const response = await fetch(`${socialPay.apiUrl}/payment/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${socialPay.secretKey}`
        },
        body: JSON.stringify({
          merchant_id: socialPay.merchantId,
          amount: request.amount,
          currency: request.currency,
          order_id: payment.id,
          description: `InnoHub Course Payment - ${payment.id}`,
          callback_url: `${process.env.NEXTAUTH_URL}/api/payments/socialpay/callback`,
          return_url: `${process.env.NEXTAUTH_URL}/courses/payment/success`
        })
      });

      const data = await response.json();
      
      if (data.success) {
        await this.updatePaymentStatus(payment.id, 'pending', data.transaction_id);
        
        return {
          success: true,
          paymentId: payment.id,
          redirectUrl: data.payment_url,
          transactionId: data.transaction_id
        };
      } else {
        throw new Error(data.message || 'SocialPay payment creation failed');
      }
    } catch (error) {
      await this.updatePaymentStatus(payment.id, 'failed');
      throw error;
    }
  }

  /**
   * MonPay integration
   */
  private async processMonPayPayment(payment: any, request: PaymentRequest): Promise<PaymentResult> {
    try {
      const { monpay } = this.mobileConfig;
      
      const response = await fetch(`${monpay.apiUrl}/payment/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Merchant-ID': monpay.merchantId,
          'X-API-Key': monpay.secretKey
        },
        body: JSON.stringify({
          amount: request.amount,
          currency: request.currency,
          order_id: payment.id,
          description: `InnoHub Course Payment - ${payment.id}`,
          callback_url: `${process.env.NEXTAUTH_URL}/api/payments/monpay/callback`,
          return_url: `${process.env.NEXTAUTH_URL}/courses/payment/success`
        })
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        await this.updatePaymentStatus(payment.id, 'pending', data.payment_id);
        
        return {
          success: true,
          paymentId: payment.id,
          redirectUrl: data.payment_url,
          qrCode: data.qr_code
        };
      } else {
        throw new Error(data.message || 'MonPay payment creation failed');
      }
    } catch (error) {
      await this.updatePaymentStatus(payment.id, 'failed');
      throw error;
    }
  }

  /**
   * Khan Bank integration
   */
  private async processKhanBankPayment(payment: any, request: PaymentRequest): Promise<PaymentResult> {
    try {
      const { khanBank } = this.bankConfig;
      
      const response = await fetch(`${khanBank.apiUrl}/payment/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Merchant-ID': khanBank.merchantId,
          'X-API-Key': khanBank.secretKey
        },
        body: JSON.stringify({
          amount: request.amount,
          currency: request.currency,
          order_id: payment.id,
          description: `InnoHub Course Payment - ${payment.id}`,
          callback_url: `${process.env.NEXTAUTH_URL}/api/payments/khan/callback`,
          return_url: `${process.env.NEXTAUTH_URL}/courses/payment/success`
        })
      });

      const data = await response.json();
      
      if (data.success) {
        await this.updatePaymentStatus(payment.id, 'pending', data.transaction_id);
        
        return {
          success: true,
          paymentId: payment.id,
          redirectUrl: data.payment_url
        };
      } else {
        throw new Error(data.message || 'Khan Bank payment creation failed');
      }
    } catch (error) {
      await this.updatePaymentStatus(payment.id, 'failed');
      throw error;
    }
  }

  /**
   * TDB Bank integration
   */
  private async processTDBBankPayment(payment: any, request: PaymentRequest): Promise<PaymentResult> {
    // Similar implementation to Khan Bank
    return this.processGenericBankPayment(payment, request, this.bankConfig.tdbBank, 'tdb');
  }

  /**
   * Golomt Bank integration
   */
  private async processGolomtBankPayment(payment: any, request: PaymentRequest): Promise<PaymentResult> {
    // Similar implementation to Khan Bank
    return this.processGenericBankPayment(payment, request, this.bankConfig.golomtBank, 'golomt');
  }

  /**
   * Generic bank payment processing
   */
  private async processGenericBankPayment(
    payment: any, 
    request: PaymentRequest, 
    config: any, 
    bankName: string
  ): Promise<PaymentResult> {
    try {
      const response = await fetch(`${config.apiUrl}/payment/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Merchant-ID': config.merchantId,
          'X-API-Key': config.secretKey
        },
        body: JSON.stringify({
          amount: request.amount,
          currency: request.currency,
          order_id: payment.id,
          description: `InnoHub Course Payment - ${payment.id}`,
          callback_url: `${process.env.NEXTAUTH_URL}/api/payments/${bankName}/callback`,
          return_url: `${process.env.NEXTAUTH_URL}/courses/payment/success`
        })
      });

      const data = await response.json();
      
      if (data.success) {
        await this.updatePaymentStatus(payment.id, 'pending', data.transaction_id);
        
        return {
          success: true,
          paymentId: payment.id,
          redirectUrl: data.payment_url
        };
      } else {
        throw new Error(data.message || `${bankName} payment creation failed`);
      }
    } catch (error) {
      await this.updatePaymentStatus(payment.id, 'failed');
      throw error;
    }
  }

  /**
   * Card payment processing (fallback)
   */
  private async processCardPayment(payment: any, request: PaymentRequest): Promise<PaymentResult> {
    // For now, redirect to a generic card payment form
    // In production, integrate with international payment processors like Stripe
    return {
      success: true,
      paymentId: payment.id,
      redirectUrl: `/courses/payment/card?payment_id=${payment.id}`
    };
  }

  /**
   * Create payment record in database
   */
  private async createPaymentRecord(request: PaymentRequest) {
    return await prisma.$transaction(async (tx) => {
      // Create payment record (you'll need to add Payment model to schema)
      const payment = await tx.$executeRaw`
        INSERT INTO payments (id, user_id, course_id, amount, currency, payment_method, status, created_at)
        VALUES (${Date.now().toString()}, ${request.userId}, ${request.courseId}, ${request.amount}, ${request.currency}, ${request.paymentMethod}, 'pending', NOW())
        RETURNING *
      `;
      
      return { id: Date.now().toString(), ...request };
    });
  }

  /**
   * Update payment status
   */
  private async updatePaymentStatus(paymentId: string, status: PaymentStatus, transactionId?: string) {
    // Update payment status in database
    console.log(`Payment ${paymentId} status updated to ${status}`, { transactionId });
  }

  /**
   * Handle payment callback/webhook
   */
  async handlePaymentCallback(
    paymentMethod: PaymentMethod, 
    callbackData: any
  ): Promise<{ success: boolean; paymentId?: string }> {
    try {
      // Verify callback authenticity based on payment method
      const isValid = await this.verifyCallback(paymentMethod, callbackData);
      
      if (!isValid) {
        throw new Error('Invalid callback signature');
      }

      // Update payment status and enroll user in course
      const paymentId = callbackData.order_id || callbackData.sender_invoice_no;
      await this.updatePaymentStatus(paymentId, 'completed', callbackData.transaction_id);
      
      // Enroll user in course
      await this.enrollUserInCourse(paymentId);
      
      return { success: true, paymentId };
    } catch (error) {
      console.error('Payment callback error:', error);
      return { success: false };
    }
  }

  /**
   * Verify payment callback signature
   */
  private async verifyCallback(paymentMethod: PaymentMethod, callbackData: any): Promise<boolean> {
    // Implement signature verification for each payment method
    // This is crucial for security
    return true; // Simplified for demo
  }

  /**
   * Enroll user in course after successful payment
   */
  private async enrollUserInCourse(paymentId: string) {
    // Get payment details and enroll user
    console.log(`Enrolling user for payment ${paymentId}`);
  }
}

export const paymentService = new PaymentService();
export default paymentService;

// Payment method configurations for UI
export const MONGOLIAN_PAYMENT_METHODS = [
  {
    id: 'qpay',
    name: 'QPay',
    nameEn: 'QPay',
    nameMn: 'Кью Пэй',
    icon: '/images/payments/qpay.png',
    description: 'Pay with QPay mobile wallet',
    descriptionMn: 'QPay цахим түрийвчээр төлөх',
    type: 'mobile',
    popular: true
  },
  {
    id: 'socialpay',
    name: 'SocialPay',
    nameEn: 'SocialPay',
    nameMn: 'Сошиал Пэй',
    icon: '/images/payments/socialpay.png',
    description: 'Pay with SocialPay',
    descriptionMn: 'SocialPay-ээр төлөх',
    type: 'mobile',
    popular: true
  },
  {
    id: 'monpay',
    name: 'MonPay',
    nameEn: 'MonPay',
    nameMn: 'Мон Пэй',
    icon: '/images/payments/monpay.png',
    description: 'Pay with MonPay',
    descriptionMn: 'MonPay-ээр төлөх',
    type: 'mobile',
    popular: false
  },
  {
    id: 'khan_bank',
    name: 'Khan Bank',
    nameEn: 'Khan Bank',
    nameMn: 'Хаан Банк',
    icon: '/images/payments/khan-bank.png',
    description: 'Pay with Khan Bank card',
    descriptionMn: 'Хаан Банкны картаар төлөх',
    type: 'bank',
    popular: true
  },
  {
    id: 'tdb_bank',
    name: 'TDB Bank',
    nameEn: 'Trade and Development Bank',
    nameMn: 'Худалдаа Хөгжлийн Банк',
    icon: '/images/payments/tdb-bank.png',
    description: 'Pay with TDB Bank card',
    descriptionMn: 'ХХБ-ны картаар төлөх',
    type: 'bank',
    popular: true
  },
  {
    id: 'golomt_bank',
    name: 'Golomt Bank',
    nameEn: 'Golomt Bank',
    nameMn: 'Голомт Банк',
    icon: '/images/payments/golomt-bank.png',
    description: 'Pay with Golomt Bank card',
    descriptionMn: 'Голомт Банкны картаар төлөх',
    type: 'bank',
    popular: true
  },
  {
    id: 'card',
    name: 'International Card',
    nameEn: 'Credit/Debit Card',
    nameMn: 'Олон улсын карт',
    icon: '/images/payments/card.png',
    description: 'Pay with Visa/Mastercard',
    descriptionMn: 'Visa/Mastercard картаар төлөх',
    type: 'card',
    popular: false
  }
] as const;
