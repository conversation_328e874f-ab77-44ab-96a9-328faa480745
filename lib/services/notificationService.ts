import emailService from './emailService';
import { prisma } from '@/lib/prisma';

export interface NotificationPreferences {
  emailNotifications: boolean;
  courseUpdates: boolean;
  progressReminders: boolean;
  promotionalEmails: boolean;
  certificateNotifications: boolean;
}

class NotificationService {
  
  /**
   * Send welcome notification to new user
   */
  async sendWelcomeNotification(userId: string): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user || !user.email) return;

      // Send welcome email
      await emailService.sendWelcomeEmail({
        email: user.email,
        name: user.name || 'Student'
      });

      // Log notification
      await this.logNotification(userId, 'welcome', 'Welcome email sent');
    } catch (error) {
      console.error('Failed to send welcome notification:', error);
    }
  }

  /**
   * Send course enrollment notification
   */
  async sendEnrollmentNotification(userId: string, courseId: string): Promise<void> {
    try {
      const [user, course] = await Promise.all([
        prisma.user.findUnique({ where: { id: userId } }),
        prisma.course.findUnique({ where: { id: courseId } })
      ]);

      if (!user || !course || !user.email) return;

      // Check user preferences
      const preferences = await this.getUserPreferences(userId);
      if (!preferences.emailNotifications || !preferences.courseUpdates) return;

      // Send enrollment email
      await emailService.sendEnrollmentConfirmation(
        { email: user.email, name: user.name || 'Student' },
        { 
          title: course.title, 
          instructor: course.instructor, 
          id: course.id 
        }
      );

      // Log notification
      await this.logNotification(userId, 'enrollment', `Enrolled in ${course.title}`);
    } catch (error) {
      console.error('Failed to send enrollment notification:', error);
    }
  }

  /**
   * Send certificate notification
   */
  async sendCertificateNotification(
    userId: string, 
    courseId: string, 
    certificateNumber: string
  ): Promise<void> {
    try {
      const [user, course] = await Promise.all([
        prisma.user.findUnique({ where: { id: userId } }),
        prisma.course.findUnique({ where: { id: courseId } })
      ]);

      if (!user || !course || !user.email) return;

      // Check user preferences
      const preferences = await this.getUserPreferences(userId);
      if (!preferences.emailNotifications || !preferences.certificateNotifications) return;

      // Send certificate email
      await emailService.sendCertificate(
        { email: user.email, name: user.name || 'Student' },
        { title: course.title, instructor: course.instructor },
        certificateNumber
      );

      // Log notification
      await this.logNotification(userId, 'certificate', `Certificate earned for ${course.title}`);
    } catch (error) {
      console.error('Failed to send certificate notification:', error);
    }
  }

  /**
   * Send progress reminder notifications
   */
  async sendProgressReminders(): Promise<void> {
    try {
      // Find users with incomplete courses (last activity > 7 days ago)
      const inactiveEnrollments = await prisma.enrollment.findMany({
        where: {
          status: 'ACTIVE',
          updatedAt: {
            lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
          }
        },
        include: {
          user: true,
          course: true
        }
      });

      for (const enrollment of inactiveEnrollments) {
        if (!enrollment.user.email) continue;

        // Check user preferences
        const preferences = await this.getUserPreferences(enrollment.userId);
        if (!preferences.emailNotifications || !preferences.progressReminders) continue;

        // Calculate progress (simplified)
        const progress = await this.calculateCourseProgress(enrollment.userId, enrollment.courseId);

        // Send reminder if progress is between 10% and 90%
        if (progress > 10 && progress < 90) {
          await emailService.sendProgressReminder(
            { email: enrollment.user.email, name: enrollment.user.name || 'Student' },
            { title: enrollment.course.title, progress }
          );

          // Log notification
          await this.logNotification(
            enrollment.userId, 
            'progress_reminder', 
            `Progress reminder for ${enrollment.course.title}`
          );
        }
      }
    } catch (error) {
      console.error('Failed to send progress reminders:', error);
    }
  }

  /**
   * Send coupon notification
   */
  async sendCouponNotification(
    userId: string, 
    couponCode: string, 
    description: string, 
    discountValue: number,
    expirationDate?: Date
  ): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user || !user.email) return;

      // Check user preferences
      const preferences = await this.getUserPreferences(userId);
      if (!preferences.emailNotifications || !preferences.promotionalEmails) return;

      // Send coupon email
      await emailService.sendCouponNotification(
        { email: user.email, name: user.name || 'Student' },
        { 
          code: couponCode, 
          description, 
          discountValue, 
          expirationDate 
        }
      );

      // Log notification
      await this.logNotification(userId, 'coupon', `Coupon sent: ${couponCode}`);
    } catch (error) {
      console.error('Failed to send coupon notification:', error);
    }
  }

  /**
   * Send payment confirmation
   */
  async sendPaymentConfirmation(
    userId: string,
    paymentData: {
      amount: number;
      currency: string;
      courseId: string;
      transactionId: string;
    }
  ): Promise<void> {
    try {
      const [user, course] = await Promise.all([
        prisma.user.findUnique({ where: { id: userId } }),
        prisma.course.findUnique({ where: { id: paymentData.courseId } })
      ]);

      if (!user || !course || !user.email) return;

      // Send payment confirmation email
      await emailService.sendPaymentConfirmation(
        { email: user.email, name: user.name || 'Student' },
        {
          amount: paymentData.amount,
          currency: paymentData.currency,
          course: { title: course.title },
          transactionId: paymentData.transactionId
        }
      );

      // Log notification
      await this.logNotification(userId, 'payment', `Payment confirmed for ${course.title}`);
    } catch (error) {
      console.error('Failed to send payment confirmation:', error);
    }
  }

  /**
   * Send bulk promotional emails
   */
  async sendPromotionalEmail(
    userIds: string[],
    subject: string,
    content: string,
    couponCode?: string
  ): Promise<void> {
    try {
      const users = await prisma.user.findMany({
        where: { 
          id: { in: userIds },
          email: { not: null }
        }
      });

      for (const user of users) {
        if (!user.email) continue;

        // Check user preferences
        const preferences = await this.getUserPreferences(user.id);
        if (!preferences.emailNotifications || !preferences.promotionalEmails) continue;

        // Send promotional email (simplified)
        await emailService.sendEmail({
          to: { email: user.email, name: user.name || 'Student' },
          template: {
            subject,
            html: content,
            text: content.replace(/<[^>]*>/g, '')
          }
        });

        // Log notification
        await this.logNotification(user.id, 'promotional', subject);
      }
    } catch (error) {
      console.error('Failed to send promotional emails:', error);
    }
  }

  /**
   * Get user notification preferences
   */
  async getUserPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      // In a real implementation, you'd have a user_preferences table
      // For now, return default preferences
      return {
        emailNotifications: true,
        courseUpdates: true,
        progressReminders: true,
        promotionalEmails: true,
        certificateNotifications: true
      };
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      return {
        emailNotifications: false,
        courseUpdates: false,
        progressReminders: false,
        promotionalEmails: false,
        certificateNotifications: false
      };
    }
  }

  /**
   * Update user notification preferences
   */
  async updateUserPreferences(
    userId: string, 
    preferences: Partial<NotificationPreferences>
  ): Promise<void> {
    try {
      // In a real implementation, you'd update the user_preferences table
      console.log(`Updated preferences for user ${userId}:`, preferences);
    } catch (error) {
      console.error('Failed to update user preferences:', error);
    }
  }

  /**
   * Calculate course progress for a user
   */
  private async calculateCourseProgress(userId: string, courseId: string): Promise<number> {
    try {
      // Get all lessons in the course
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          modules: {
            include: {
              lessons: true
            }
          }
        }
      });

      if (!course) return 0;

      const allLessons = course.modules.flatMap(module => module.lessons);
      const totalLessons = allLessons.length;

      if (totalLessons === 0) return 0;

      // Get completed lessons
      const completedProgress = await prisma.lessonProgress.findMany({
        where: {
          userId,
          lessonId: { in: allLessons.map(lesson => lesson.id) },
          isCompleted: true
        }
      });

      const completedLessons = completedProgress.length;
      return Math.round((completedLessons / totalLessons) * 100);
    } catch (error) {
      console.error('Failed to calculate course progress:', error);
      return 0;
    }
  }

  /**
   * Log notification for tracking
   */
  private async logNotification(
    userId: string, 
    type: string, 
    message: string
  ): Promise<void> {
    try {
      // In a real implementation, you'd have a notifications table
      console.log(`Notification logged: ${type} for user ${userId} - ${message}`);
    } catch (error) {
      console.error('Failed to log notification:', error);
    }
  }

  /**
   * Get user notification history
   */
  async getNotificationHistory(userId: string, limit = 50): Promise<any[]> {
    try {
      // In a real implementation, you'd query the notifications table
      return [];
    } catch (error) {
      console.error('Failed to get notification history:', error);
      return [];
    }
  }

  /**
   * Schedule automated notifications (to be called by cron job)
   */
  async processScheduledNotifications(): Promise<void> {
    try {
      // Send progress reminders
      await this.sendProgressReminders();

      // Send other scheduled notifications
      console.log('Scheduled notifications processed');
    } catch (error) {
      console.error('Failed to process scheduled notifications:', error);
    }
  }
}

export const notificationService = new NotificationService();
export default notificationService;
