import { prisma } from '@/lib/prisma';
import { Course, Module, Lesson, Enrollment, LessonProgress } from '@prisma/client';

export interface CourseWithModules extends Course {
  modules: (Module & {
    lessons: Lesson[];
  })[];
}

export interface EnrollmentWithCourse extends Enrollment {
  course: Course;
}

export interface LessonWithProgress extends Lesson {
  progress?: LessonProgress[];
}

class CourseService {
  
  /**
   * Get all published courses
   */
  async getAllCourses(): Promise<Course[]> {
    try {
      return await prisma.course.findMany({
        where: { isPublished: true },
        orderBy: { createdAt: 'desc' }
      });
    } catch (error) {
      console.error('Failed to fetch courses:', error);
      return [];
    }
  }

  /**
   * Get course by ID with modules and lessons
   */
  async getCourseById(courseId: string): Promise<CourseWithModules | null> {
    try {
      return await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          modules: {
            include: {
              lessons: {
                orderBy: { order: 'asc' }
              }
            },
            orderBy: { order: 'asc' }
          }
        }
      });
    } catch (error) {
      console.error('Failed to fetch course:', error);
      return null;
    }
  }

  /**
   * Get user's enrolled courses
   */
  async getUserEnrollments(userId: string): Promise<EnrollmentWithCourse[]> {
    try {
      return await prisma.enrollment.findMany({
        where: { userId },
        include: { course: true },
        orderBy: { enrolledAt: 'desc' }
      });
    } catch (error) {
      console.error('Failed to fetch enrollments:', error);
      return [];
    }
  }

  /**
   * Enroll user in a course
   */
  async enrollUser(userId: string, courseId: string): Promise<Enrollment | null> {
    try {
      // Check if already enrolled
      const existingEnrollment = await prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId,
            courseId
          }
        }
      });

      if (existingEnrollment) {
        return existingEnrollment;
      }

      return await prisma.enrollment.create({
        data: {
          userId,
          courseId,
          status: 'ACTIVE'
        }
      });
    } catch (error) {
      console.error('Failed to enroll user:', error);
      return null;
    }
  }

  /**
   * Get lesson progress for a user
   */
  async getLessonProgress(userId: string, lessonId: string): Promise<LessonProgress | null> {
    try {
      return await prisma.lessonProgress.findUnique({
        where: {
          userId_lessonId: {
            userId,
            lessonId
          }
        }
      });
    } catch (error) {
      console.error('Failed to fetch lesson progress:', error);
      return null;
    }
  }

  /**
   * Update lesson progress
   */
  async updateLessonProgress(
    userId: string, 
    lessonId: string, 
    data: {
      watchTime?: number;
      lastPosition?: number;
      isCompleted?: boolean;
    }
  ): Promise<LessonProgress | null> {
    try {
      return await prisma.lessonProgress.upsert({
        where: {
          userId_lessonId: {
            userId,
            lessonId
          }
        },
        update: {
          ...data,
          completedAt: data.isCompleted ? new Date() : undefined,
          updatedAt: new Date()
        },
        create: {
          userId,
          lessonId,
          ...data,
          completedAt: data.isCompleted ? new Date() : undefined
        }
      });
    } catch (error) {
      console.error('Failed to update lesson progress:', error);
      return null;
    }
  }

  /**
   * Get course progress for a user
   */
  async getCourseProgress(userId: string, courseId: string): Promise<{
    totalLessons: number;
    completedLessons: number;
    progressPercentage: number;
  }> {
    try {
      // Get all lessons in the course
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          modules: {
            include: {
              lessons: true
            }
          }
        }
      });

      if (!course) {
        return { totalLessons: 0, completedLessons: 0, progressPercentage: 0 };
      }

      const allLessons = course.modules.flatMap(module => module.lessons);
      const totalLessons = allLessons.length;

      // Get completed lessons
      const completedProgress = await prisma.lessonProgress.findMany({
        where: {
          userId,
          lessonId: { in: allLessons.map(lesson => lesson.id) },
          isCompleted: true
        }
      });

      const completedLessons = completedProgress.length;
      const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;

      return {
        totalLessons,
        completedLessons,
        progressPercentage
      };
    } catch (error) {
      console.error('Failed to calculate course progress:', error);
      return { totalLessons: 0, completedLessons: 0, progressPercentage: 0 };
    }
  }

  /**
   * Search courses
   */
  async searchCourses(query: string, filters?: {
    category?: string;
    level?: string;
    priceRange?: [number, number];
  }): Promise<Course[]> {
    try {
      const where: any = {
        isPublished: true,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { instructor: { contains: query, mode: 'insensitive' } }
        ]
      };

      if (filters?.category && filters.category !== 'All') {
        where.category = filters.category;
      }

      if (filters?.level && filters.level !== 'All') {
        where.level = filters.level;
      }

      if (filters?.priceRange) {
        where.price = {
          gte: filters.priceRange[0],
          lte: filters.priceRange[1]
        };
      }

      return await prisma.course.findMany({
        where,
        orderBy: { createdAt: 'desc' }
      });
    } catch (error) {
      console.error('Failed to search courses:', error);
      return [];
    }
  }

  /**
   * Get user's dashboard stats
   */
  async getUserStats(userId: string): Promise<{
    enrolledCourses: number;
    completedCourses: number;
    totalWatchTime: number;
    currentStreak: number;
  }> {
    try {
      const enrollments = await prisma.enrollment.findMany({
        where: { userId },
        include: { course: true }
      });

      const enrolledCourses = enrollments.length;
      const completedCourses = enrollments.filter(e => e.status === 'COMPLETED').length;

      // Calculate total watch time
      const allProgress = await prisma.lessonProgress.findMany({
        where: { userId }
      });

      const totalWatchTime = allProgress.reduce((sum, progress) => sum + progress.watchTime, 0);

      // Calculate current streak (simplified - days with activity)
      const recentProgress = await prisma.lessonProgress.findMany({
        where: {
          userId,
          updatedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        orderBy: { updatedAt: 'desc' }
      });

      // Simple streak calculation - consecutive days with activity
      const currentStreak = this.calculateStreak(recentProgress);

      return {
        enrolledCourses,
        completedCourses,
        totalWatchTime,
        currentStreak
      };
    } catch (error) {
      console.error('Failed to fetch user stats:', error);
      return {
        enrolledCourses: 0,
        completedCourses: 0,
        totalWatchTime: 0,
        currentStreak: 0
      };
    }
  }

  private calculateStreak(progress: LessonProgress[]): number {
    if (progress.length === 0) return 0;

    const today = new Date();
    const dates = progress.map(p => new Date(p.updatedAt).toDateString());
    const uniqueDates = [...new Set(dates)].sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let streak = 0;
    let currentDate = today;

    for (const dateStr of uniqueDates) {
      const date = new Date(dateStr);
      const diffDays = Math.floor((currentDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

      if (diffDays === streak) {
        streak++;
        currentDate = date;
      } else {
        break;
      }
    }

    return streak;
  }
}

export const courseService = new CourseService();
export default courseService;

// Database seeder function
export async function seedCourses() {
  try {
    // Check if courses already exist
    const existingCourses = await prisma.course.count();
    if (existingCourses > 0) {
      console.log('Courses already exist, skipping seed');
      return;
    }

    const coursesData = [
      {
        title: 'Entrepreneurship Fundamentals',
        description: 'Learn the basics of starting and running a successful business',
        longDescription: 'This comprehensive course covers everything you need to know about entrepreneurship, from idea validation to business model development, market research, and scaling strategies.',
        thumbnail: '/images/courses/entrepreneurship.jpg',
        duration: '8 hours',
        level: 'BEGINNER' as const,
        category: 'Business',
        instructor: 'Sarah Johnson',
        price: 99,
        isPublished: true,
        modules: [
          {
            title: 'Introduction to Entrepreneurship',
            order: 1,
            lessons: [
              {
                title: 'What is Entrepreneurship?',
                description: 'Understanding the entrepreneurial mindset and opportunities',
                videoId: 'dQw4w9WgXcQ',
                duration: '15:30',
                order: 1
              },
              {
                title: 'Types of Entrepreneurs',
                description: 'Different entrepreneurial paths and business models',
                videoId: 'dQw4w9WgXcQ',
                duration: '12:45',
                order: 2
              }
            ]
          },
          {
            title: 'Business Idea Development',
            order: 2,
            lessons: [
              {
                title: 'Idea Generation Techniques',
                description: 'Methods for generating innovative business ideas',
                videoId: 'dQw4w9WgXcQ',
                duration: '18:20',
                order: 1
              },
              {
                title: 'Market Validation',
                description: 'How to validate your business idea with real customers',
                videoId: 'dQw4w9WgXcQ',
                duration: '22:15',
                order: 2
              }
            ]
          }
        ]
      },
      {
        title: 'Digital Marketing Mastery',
        description: 'Complete guide to digital marketing strategies and tactics',
        longDescription: 'Master the art of digital marketing with hands-on training in SEO, social media marketing, content marketing, email marketing, and paid advertising.',
        thumbnail: '/images/courses/digital-marketing.jpg',
        duration: '12 hours',
        level: 'INTERMEDIATE' as const,
        category: 'Marketing',
        instructor: 'Mike Chen',
        price: 149,
        isPublished: true,
        modules: [
          {
            title: 'Digital Marketing Foundations',
            order: 1,
            lessons: [
              {
                title: 'Digital Marketing Overview',
                description: 'Introduction to digital marketing channels and strategies',
                videoId: 'dQw4w9WgXcQ',
                duration: '20:00',
                order: 1
              },
              {
                title: 'Customer Journey Mapping',
                description: 'Understanding how customers interact with your brand online',
                videoId: 'dQw4w9WgXcQ',
                duration: '25:30',
                order: 2
              }
            ]
          }
        ]
      },
      {
        title: 'Product Development & Design',
        description: 'Learn to build products that customers love',
        longDescription: 'From concept to launch, learn the complete product development process including user research, prototyping, testing, and iteration.',
        thumbnail: '/images/courses/product-development.jpg',
        duration: '10 hours',
        level: 'ADVANCED' as const,
        category: 'Product',
        instructor: 'Emily Rodriguez',
        price: 199,
        isPublished: true,
        modules: [
          {
            title: 'Product Strategy',
            order: 1,
            lessons: [
              {
                title: 'Product Vision & Strategy',
                description: 'Defining your product vision and strategic roadmap',
                videoId: 'dQw4w9WgXcQ',
                duration: '30:00',
                order: 1
              }
            ]
          }
        ]
      }
    ];

    for (const courseData of coursesData) {
      const { modules, ...courseInfo } = courseData;

      const course = await prisma.course.create({
        data: courseInfo
      });

      for (const moduleData of modules) {
        const { lessons, ...moduleInfo } = moduleData;

        const module = await prisma.module.create({
          data: {
            ...moduleInfo,
            courseId: course.id
          }
        });

        for (const lessonData of lessons) {
          await prisma.lesson.create({
            data: {
              ...lessonData,
              moduleId: module.id
            }
          });
        }
      }
    }

    console.log('Database seeded successfully with courses');
  } catch (error) {
    console.error('Failed to seed database:', error);
  }
}
