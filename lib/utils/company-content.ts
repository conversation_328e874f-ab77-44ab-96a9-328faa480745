import { Company, ProgramTrack, Milestone } from '@/lib/data/companies';

// Content templates based on program track
export const getWelcomeMessage = (company: Company): string => {
  const trackMessages = {
    'accelerator': `Welcome to the Accelerator Program! Over the next 8 months, you'll work closely with our team to scale ${company.name} from ${company.currentStage} to the next level. Our proven methodology has helped hundreds of startups achieve significant growth and secure funding.`,
    'mongolian-idea': `Welcome to the Mongolian Intellectual Idea program! This 3-month intensive journey will help transform your innovative concept into a viable business. You'll learn essential entrepreneurship skills while developing ${company.name} with guidance from experienced mentors.`,
    'growth': `Welcome to the Growth Program! As an established business, ${company.name} is ready for the next phase of scaling. Over 6 months, we'll focus on advanced growth strategies, market expansion, and operational excellence to accelerate your trajectory.`,
    'corporate': `Welcome to the Corporate Innovation Program! This 4-month program is designed to help ${company.name} drive innovation within the corporate environment. You'll learn to navigate corporate structures while implementing cutting-edge solutions.`
  };

  return trackMessages[company.programTrack.id as keyof typeof trackMessages] || 
    `Welcome to ${company.programTrack.name}! We're excited to support ${company.name} on this entrepreneurial journey.`;
};

// Generate stage-specific next steps
export const getStageSpecificSteps = (company: Company): string[] => {
  const stageSteps = {
    'Idea': [
      'Validate your business concept with potential customers',
      'Complete market research and competitive analysis',
      'Develop your minimum viable product (MVP)',
      'Create initial business model canvas'
    ],
    'MVP Development': [
      'Complete MVP development and testing',
      'Gather user feedback and iterate',
      'Develop go-to-market strategy',
      'Prepare for pilot customer acquisition'
    ],
    'Pre-Seed': [
      'Refine product-market fit',
      'Build initial customer base',
      'Prepare pitch deck and financial projections',
      'Network with potential investors'
    ],
    'Seed Funding': [
      'Execute fundraising strategy',
      'Scale customer acquisition',
      'Build core team',
      'Establish key partnerships'
    ],
    'Series A+': [
      'Optimize unit economics',
      'Expand to new markets',
      'Scale operations and team',
      'Prepare for next funding round'
    ]
  };

  return stageSteps[company.currentStage as keyof typeof stageSteps] || company.nextSteps;
};

// Generate program-specific resources
export const getProgramResources = (programTrack: ProgramTrack) => {
  const trackResources = {
    'accelerator': {
      courses: [
        'Advanced Business Strategy',
        'Fundraising Masterclass',
        'Scaling Operations',
        'Leadership Development',
        'Financial Management for Startups'
      ],
      documents: [
        'Series A Pitch Deck Template',
        'Financial Model Template',
        'Legal Documents Checklist',
        'Investor Database',
        'Due Diligence Checklist'
      ],
      tools: [
        'CRM Platform (HubSpot)',
        'Analytics Suite (Mixpanel)',
        'Design Tools (Figma Pro)',
        'Development Tools (GitHub Enterprise)',
        'Communication (Slack Premium)'
      ]
    },
    'mongolian-idea': {
      courses: [
        'Entrepreneurship Fundamentals',
        'Business Model Design',
        'Customer Development',
        'MVP Development',
        'Presentation Skills'
      ],
      documents: [
        'Business Model Canvas',
        'Customer Interview Guide',
        'MVP Planning Template',
        'Pitch Presentation Template',
        'Market Research Framework'
      ],
      tools: [
        'Design Tools (Canva Pro)',
        'Survey Platform (Typeform)',
        'Project Management (Notion)',
        'Video Conferencing (Zoom)',
        'File Storage (Google Workspace)'
      ]
    },
    'growth': {
      courses: [
        'Growth Hacking Strategies',
        'International Expansion',
        'Advanced Marketing',
        'Operations Optimization',
        'M&A Fundamentals'
      ],
      documents: [
        'Growth Strategy Template',
        'Market Expansion Playbook',
        'KPI Dashboard Template',
        'Partnership Agreement Templates',
        'Exit Strategy Guide'
      ],
      tools: [
        'Advanced Analytics (Amplitude)',
        'Marketing Automation (Marketo)',
        'Business Intelligence (Tableau)',
        'Enterprise CRM (Salesforce)',
        'Financial Planning (PlanGuru)'
      ]
    },
    'corporate': {
      courses: [
        'Corporate Innovation Management',
        'Digital Transformation',
        'Change Management',
        'Innovation Metrics',
        'Stakeholder Management'
      ],
      documents: [
        'Innovation Framework',
        'Change Management Guide',
        'ROI Calculation Templates',
        'Stakeholder Mapping',
        'Innovation Metrics Dashboard'
      ],
      tools: [
        'Innovation Platform (IdeaScale)',
        'Project Management (Microsoft Project)',
        'Collaboration (Microsoft Teams)',
        'Analytics (Power BI)',
        'Document Management (SharePoint)'
      ]
    }
  };

  return trackResources[programTrack.id as keyof typeof trackResources] || {
    courses: [],
    documents: [],
    tools: []
  };
};

// Generate milestone templates based on program and stage
export const generateMilestones = (company: Company): Milestone[] => {
  const baseMilestones = {
    'accelerator': [
      {
        title: 'Market Validation',
        description: 'Complete comprehensive market research and validate product-market fit',
        priority: 'high' as const,
        resources: ['Market Research Template', 'Customer Interview Guide']
      },
      {
        title: 'Product Development',
        description: 'Develop and launch minimum viable product with core features',
        priority: 'high' as const,
        resources: ['MVP Development Guide', 'Technical Architecture Template']
      },
      {
        title: 'Customer Acquisition',
        description: 'Acquire first 100 paying customers and establish retention metrics',
        priority: 'medium' as const,
        resources: ['Customer Acquisition Playbook', 'Metrics Dashboard']
      },
      {
        title: 'Fundraising Preparation',
        description: 'Prepare pitch deck, financial models, and investor materials',
        priority: 'medium' as const,
        resources: ['Pitch Deck Template', 'Financial Model Template']
      }
    ],
    'mongolian-idea': [
      {
        title: 'Idea Validation',
        description: 'Validate business idea through customer interviews and market research',
        priority: 'high' as const,
        resources: ['Validation Framework', 'Interview Templates']
      },
      {
        title: 'Business Model Design',
        description: 'Create comprehensive business model canvas and value proposition',
        priority: 'high' as const,
        resources: ['Business Model Canvas', 'Value Proposition Template']
      },
      {
        title: 'Prototype Development',
        description: 'Build initial prototype or proof of concept',
        priority: 'medium' as const,
        resources: ['Prototyping Guide', 'Design Tools Access']
      }
    ]
  };

  const milestones = baseMilestones[company.programTrack.id as keyof typeof baseMilestones] || [];
  
  return milestones.map((milestone, index) => ({
    id: `milestone-${index + 1}`,
    title: milestone.title,
    description: milestone.description,
    dueDate: new Date(Date.now() + (index + 1) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days apart
    status: index === 0 ? 'in-progress' as const : 'pending' as const,
    priority: milestone.priority,
    resources: milestone.resources
  }));
};

// Get program-specific FAQ
export const getProgramFAQ = (programTrack: ProgramTrack) => {
  const trackFAQ = {
    'accelerator': [
      {
        question: 'What is the time commitment for the Accelerator Program?',
        answer: 'The program requires approximately 20-30 hours per week, including mentor meetings, workshops, and milestone work. Most activities are flexible and can be scheduled around your existing commitments.'
      },
      {
        question: 'What equity does InnoHub take in my company?',
        answer: 'InnoHub typically takes 6-8% equity in exchange for the accelerator program, mentorship, and access to our investor network. The exact terms are discussed during the application process.'
      }
    ],
    'mongolian-idea': [
      {
        question: 'Do I need a technical background to participate?',
        answer: 'No technical background is required. The program is designed for students and young entrepreneurs from all backgrounds. We provide technical mentorship and resources as needed.'
      },
      {
        question: 'Can I participate while still in university?',
        answer: 'Yes! The program is specifically designed for students and can be completed alongside your studies. We offer flexible scheduling and online resources.'
      }
    ]
  };

  return trackFAQ[programTrack.id as keyof typeof trackFAQ] || [];
};

// Content personalization based on company data
export const personalizeContent = (company: Company) => {
  return {
    welcomeMessage: getWelcomeMessage(company),
    stageSpecificSteps: getStageSpecificSteps(company),
    programResources: getProgramResources(company.programTrack),
    suggestedMilestones: generateMilestones(company),
    programFAQ: getProgramFAQ(company.programTrack)
  };
};
