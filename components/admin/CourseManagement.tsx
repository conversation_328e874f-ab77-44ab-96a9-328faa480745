'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Search, 
  BookOpen,
  Users,
  DollarSign,
  Clock,
  Star,
  Save,
  X,
  PlayCircle,
  FileText,
  Award
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { useSession } from 'next-auth/react';
import courseService from '@/lib/services/courseService';

interface Course {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  thumbnail?: string;
  duration: string;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  category: string;
  instructor: string;
  price: number;
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
  modules?: Module[];
  enrollments?: any[];
}

interface Module {
  id: string;
  title: string;
  order: number;
  lessons: Lesson[];
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  videoId: string;
  duration: string;
  order: number;
}

interface CreateCourseData {
  title: string;
  description: string;
  longDescription: string;
  thumbnail: string;
  duration: string;
  level: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  category: string;
  instructor: string;
  price: number;
  isPublished: boolean;
}

export default function CourseManagement() {
  const { language } = useLanguage();
  const { data: session } = useSession();
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [showModulesModal, setShowModulesModal] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);

  // Form state
  const [formData, setFormData] = useState<CreateCourseData>({
    title: '',
    description: '',
    longDescription: '',
    thumbnail: '',
    duration: '',
    level: 'BEGINNER',
    category: 'Business',
    instructor: '',
    price: 0,
    isPublished: false
  });

  const categories = [
    'Business', 'Marketing', 'Technology', 'Design', 'Finance', 'Product'
  ];

  const levels = [
    { value: 'BEGINNER', label: 'Beginner' },
    { value: 'INTERMEDIATE', label: 'Intermediate' },
    { value: 'ADVANCED', label: 'Advanced' }
  ];

  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    setIsLoading(true);
    try {
      const coursesData = await courseService.getAllCourses();
      setCourses(coursesData);
    } catch (error) {
      console.error('Failed to load courses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateCourse = async () => {
    try {
      // In a real implementation, you'd call courseService.createCourse
      console.log('Creating course:', formData);
      setShowCreateModal(false);
      resetForm();
      loadCourses();
    } catch (error) {
      console.error('Failed to create course:', error);
    }
  };

  const handleUpdateCourse = async () => {
    if (!editingCourse) return;

    try {
      // In a real implementation, you'd call courseService.updateCourse
      console.log('Updating course:', editingCourse.id, formData);
      setEditingCourse(null);
      resetForm();
      loadCourses();
    } catch (error) {
      console.error('Failed to update course:', error);
    }
  };

  const handleDeleteCourse = async (courseId: string) => {
    if (!confirm('Are you sure you want to delete this course?')) return;

    try {
      // In a real implementation, you'd call courseService.deleteCourse
      console.log('Deleting course:', courseId);
      loadCourses();
    } catch (error) {
      console.error('Failed to delete course:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      longDescription: '',
      thumbnail: '',
      duration: '',
      level: 'BEGINNER',
      category: 'Business',
      instructor: '',
      price: 0,
      isPublished: false
    });
  };

  const openEditModal = (course: Course) => {
    setEditingCourse(course);
    setFormData({
      title: course.title,
      description: course.description,
      longDescription: course.longDescription || '',
      thumbnail: course.thumbnail || '',
      duration: course.duration,
      level: course.level,
      category: course.category,
      instructor: course.instructor,
      price: course.price,
      isPublished: course.isPublished
    });
  };

  const openModulesModal = async (course: Course) => {
    try {
      const courseWithModules = await courseService.getCourseById(course.id);
      if (courseWithModules) {
        setSelectedCourse(courseWithModules);
        setShowModulesModal(true);
      }
    } catch (error) {
      console.error('Failed to load course modules:', error);
    }
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || course.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const CourseForm = () => (
    <div className="space-y-6 max-h-[80vh] overflow-y-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title" className="text-white">Course Title *</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            placeholder="Enter course title..."
            className="bg-black/40 border-purple-500/20 text-white"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="instructor" className="text-white">Instructor *</Label>
          <Input
            id="instructor"
            value={formData.instructor}
            onChange={(e) => setFormData({ ...formData, instructor: e.target.value })}
            placeholder="Instructor name..."
            className="bg-black/40 border-purple-500/20 text-white"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description" className="text-white">Short Description *</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Brief description of the course..."
          rows={3}
          className="bg-black/40 border-purple-500/20 text-white"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="longDescription" className="text-white">Detailed Description</Label>
        <Textarea
          id="longDescription"
          value={formData.longDescription}
          onChange={(e) => setFormData({ ...formData, longDescription: e.target.value })}
          placeholder="Detailed course description..."
          rows={5}
          className="bg-black/40 border-purple-500/20 text-white"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category" className="text-white">Category</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
            <SelectTrigger className="bg-black/40 border-purple-500/20 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="level" className="text-white">Level</Label>
          <Select value={formData.level} onValueChange={(value: any) => setFormData({ ...formData, level: value })}>
            <SelectTrigger className="bg-black/40 border-purple-500/20 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {levels.map((level) => (
                <SelectItem key={level.value} value={level.value}>{level.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="duration" className="text-white">Duration</Label>
          <Input
            id="duration"
            value={formData.duration}
            onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
            placeholder="e.g., 8 hours"
            className="bg-black/40 border-purple-500/20 text-white"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="price" className="text-white">Price (MNT)</Label>
          <Input
            id="price"
            type="number"
            value={formData.price}
            onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
            placeholder="0"
            className="bg-black/40 border-purple-500/20 text-white"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="thumbnail" className="text-white">Thumbnail URL</Label>
          <Input
            id="thumbnail"
            value={formData.thumbnail}
            onChange={(e) => setFormData({ ...formData, thumbnail: e.target.value })}
            placeholder="https://example.com/image.jpg"
            className="bg-black/40 border-purple-500/20 text-white"
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="published"
          checked={formData.isPublished}
          onCheckedChange={(checked) => setFormData({ ...formData, isPublished: checked })}
        />
        <Label htmlFor="published" className="text-white">Publish course</Label>
      </div>

      <div className="flex justify-end gap-3">
        <Button
          onClick={() => {
            setShowCreateModal(false);
            setEditingCourse(null);
            resetForm();
          }}
          variant="outline"
          className="border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
        >
          Cancel
        </Button>
        <Button
          onClick={editingCourse ? handleUpdateCourse : handleCreateCourse}
          className="bg-purple-500 hover:bg-purple-600 text-white"
        >
          <Save className="h-4 w-4 mr-2" />
          {editingCourse ? 'Update Course' : 'Create Course'}
        </Button>
      </div>
    </div>
  );

  const ModulesModal = () => (
    <Dialog open={showModulesModal} onOpenChange={setShowModulesModal}>
      <DialogContent className="bg-black/95 border-purple-500/20 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Course Content: {selectedCourse?.title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {selectedCourse?.modules?.map((module, moduleIndex) => (
            <Card key={module.id} className="border-purple-500/20 bg-black/40">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-purple-400" />
                  Module {module.order}: {module.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {module.lessons.map((lesson, lessonIndex) => (
                    <div key={lesson.id} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                      <div className="flex items-center gap-3">
                        <PlayCircle className="h-4 w-4 text-purple-400" />
                        <div>
                          <p className="text-white font-medium">{lesson.title}</p>
                          <p className="text-gray-400 text-sm">{lesson.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="border-purple-500/30 text-purple-400">
                          {lesson.duration}
                        </Badge>
                        <Button variant="ghost" size="sm" className="text-blue-400 hover:text-blue-300">
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
          
          <Button className="w-full bg-purple-500 hover:bg-purple-600 text-white">
            <Plus className="h-4 w-4 mr-2" />
            Add New Module
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            {language === 'mn' ? 'Хичээл удирдлага' : 'Course Management'}
          </h1>
          <p className="text-gray-400">
            {language === 'mn' ? 'Хичээлүүдийг удирдах' : 'Manage courses and content'}
          </p>
        </div>
        
        <Button
          onClick={() => setShowCreateModal(true)}
          className="bg-purple-500 hover:bg-purple-600 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          {language === 'mn' ? 'Шинэ хичээл' : 'New Course'}
        </Button>
      </div>

      {/* Filters */}
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={language === 'mn' ? 'Хичээл хайх...' : 'Search courses...'}
                  className="pl-10 bg-black/40 border-purple-500/20 text-white"
                />
              </div>
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48 bg-black/40 border-purple-500/20 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Courses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="animate-pulse">
              <Card className="border-purple-500/20 bg-black/60">
                <div className="aspect-video bg-gray-700/50 rounded-t-lg"></div>
                <CardContent className="p-4 space-y-3">
                  <div className="h-4 bg-gray-700/50 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-700/50 rounded w-full"></div>
                  <div className="h-3 bg-gray-700/50 rounded w-2/3"></div>
                </CardContent>
              </Card>
            </div>
          ))
        ) : filteredCourses.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <BookOpen className="h-16 w-16 text-gray-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No courses found</h3>
            <p className="text-gray-400">Create your first course to get started.</p>
          </div>
        ) : (
          filteredCourses.map((course) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md hover:border-purple-500/40 transition-all duration-300 overflow-hidden group">
                {/* Course Thumbnail */}
                <div className="relative aspect-video overflow-hidden">
                  {course.thumbnail ? (
                    <img
                      src={course.thumbnail}
                      alt={course.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center">
                      <BookOpen className="h-12 w-12 text-purple-400" />
                    </div>
                  )}
                  
                  <div className="absolute top-2 right-2">
                    <Badge 
                      variant={course.isPublished ? "default" : "secondary"}
                      className={course.isPublished ? "bg-green-500/20 text-green-400" : "bg-gray-500/20 text-gray-400"}
                    >
                      {course.isPublished ? 'Published' : 'Draft'}
                    </Badge>
                  </div>
                </div>

                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-white line-clamp-2">{course.title}</h3>
                      <p className="text-gray-400 text-sm">by {course.instructor}</p>
                    </div>

                    <p className="text-gray-300 text-sm line-clamp-2">{course.description}</p>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1 text-gray-400">
                          <Clock className="h-3 w-3" />
                          <span>{course.duration}</span>
                        </div>
                        <div className="flex items-center gap-1 text-gray-400">
                          <Users className="h-3 w-3" />
                          <span>{course.enrollments?.length || 0}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 text-purple-400">
                        <DollarSign className="h-3 w-3" />
                        <span>{course.price.toLocaleString()}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="border-purple-500/30 text-purple-400">
                        {course.level}
                      </Badge>
                      <Badge variant="outline" className="border-gray-500/30 text-gray-400">
                        {course.category}
                      </Badge>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        onClick={() => openModulesModal(course)}
                        variant="outline"
                        size="sm"
                        className="flex-1 border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                      >
                        <FileText className="h-3 w-3 mr-1" />
                        Content
                      </Button>
                      <Button
                        onClick={() => openEditModal(course)}
                        variant="outline"
                        size="sm"
                        className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        onClick={() => handleDeleteCourse(course.id)}
                        variant="outline"
                        size="sm"
                        className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Create/Edit Course Modal */}
      <Dialog open={showCreateModal || !!editingCourse} onOpenChange={(open) => {
        if (!open) {
          setShowCreateModal(false);
          setEditingCourse(null);
          resetForm();
        }
      }}>
        <DialogContent className="bg-black/95 border-purple-500/20 text-white max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {editingCourse ? 'Edit Course' : 'Create New Course'}
            </DialogTitle>
          </DialogHeader>
          <CourseForm />
        </DialogContent>
      </Dialog>

      {/* Modules Modal */}
      <ModulesModal />
    </div>
  );
}
