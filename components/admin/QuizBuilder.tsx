'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Trash2, 
  Save, 
  Eye, 
  Clock, 
  Award,
  GripVertical,
  Copy
} from 'lucide-react';
import assessmentService from '@/lib/services/assessmentService';

interface Question {
  id: string;
  question: string;
  type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY';
  options?: string[];
  correctAnswer: string;
  points: number;
}

interface QuizBuilderProps {
  courseId: string;
  onSave?: (assessmentId: string) => void;
  onCancel?: () => void;
}

export default function QuizBuilder({ courseId, onSave, onCancel }: QuizBuilderProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [passingScore, setPassingScore] = useState(70);
  const [timeLimit, setTimeLimit] = useState<number | null>(null);
  const [isRequired, setIsRequired] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  const addQuestion = () => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      question: '',
      type: 'MULTIPLE_CHOICE',
      options: ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
      correctAnswer: '',
      points: 1
    };
    setQuestions([...questions, newQuestion]);
  };

  const updateQuestion = (id: string, updates: Partial<Question>) => {
    setQuestions(questions.map(q => 
      q.id === id ? { ...q, ...updates } : q
    ));
  };

  const deleteQuestion = (id: string) => {
    setQuestions(questions.filter(q => q.id !== id));
  };

  const duplicateQuestion = (id: string) => {
    const question = questions.find(q => q.id === id);
    if (question) {
      const duplicate = {
        ...question,
        id: Date.now().toString(),
        question: question.question + ' (Copy)'
      };
      setQuestions([...questions, duplicate]);
    }
  };

  const addOption = (questionId: string) => {
    const question = questions.find(q => q.id === questionId);
    if (question && question.options) {
      updateQuestion(questionId, {
        options: [...question.options, `Option ${question.options.length + 1}`]
      });
    }
  };

  const removeOption = (questionId: string, optionIndex: number) => {
    const question = questions.find(q => q.id === questionId);
    if (question && question.options && question.options.length > 2) {
      const newOptions = question.options.filter((_, index) => index !== optionIndex);
      updateQuestion(questionId, { options: newOptions });
    }
  };

  const updateOption = (questionId: string, optionIndex: number, value: string) => {
    const question = questions.find(q => q.id === questionId);
    if (question && question.options) {
      const newOptions = [...question.options];
      newOptions[optionIndex] = value;
      updateQuestion(questionId, { options: newOptions });
    }
  };

  const handleSave = async () => {
    if (!title.trim() || questions.length === 0) {
      alert('Please provide a title and at least one question.');
      return;
    }

    setIsSaving(true);
    try {
      const assessment = await assessmentService.createAssessment({
        title,
        description,
        courseId,
        passingScore,
        timeLimit,
        isRequired,
        questions: questions.map(q => ({
          question: q.question,
          type: q.type,
          options: q.options,
          correctAnswer: q.correctAnswer,
          points: q.points
        }))
      });

      if (assessment) {
        onSave?.(assessment.id);
      }
    } catch (error) {
      console.error('Failed to save assessment:', error);
      alert('Failed to save assessment. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const getTotalPoints = () => {
    return questions.reduce((sum, q) => sum + q.points, 0);
  };

  if (previewMode) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white">Assessment Preview</h2>
          <Button
            onClick={() => setPreviewMode(false)}
            variant="outline"
            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
          >
            Back to Editor
          </Button>
        </div>
        
        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-white">{title}</CardTitle>
            <p className="text-gray-400">{description}</p>
            <div className="flex gap-4 text-sm text-gray-400">
              <span>Passing Score: {passingScore}%</span>
              {timeLimit && <span>Time Limit: {timeLimit} minutes</span>}
              <span>Total Points: {getTotalPoints()}</span>
            </div>
          </CardHeader>
        </Card>

        {questions.map((question, index) => (
          <Card key={question.id} className="border-purple-500/20 bg-black/60 backdrop-blur-md">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <Badge variant="outline" className="border-purple-500/30 text-purple-400">
                  {question.points} {question.points === 1 ? 'point' : 'points'}
                </Badge>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white mb-4">
                    {index + 1}. {question.question}
                  </h3>
                  
                  {question.type === 'MULTIPLE_CHOICE' && question.options && (
                    <div className="space-y-2">
                      {question.options.map((option, optionIndex) => (
                        <div 
                          key={optionIndex} 
                          className={`p-3 rounded-lg border ${
                            option === question.correctAnswer 
                              ? 'border-green-500/50 bg-green-500/10' 
                              : 'border-gray-600/30 bg-gray-700/20'
                          }`}
                        >
                          <span className="text-white">{option}</span>
                          {option === question.correctAnswer && (
                            <Badge className="ml-2 bg-green-500/20 text-green-400">Correct</Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {question.type === 'TRUE_FALSE' && (
                    <div className="space-y-2">
                      <div className={`p-3 rounded-lg border ${
                        'true' === question.correctAnswer 
                          ? 'border-green-500/50 bg-green-500/10' 
                          : 'border-gray-600/30 bg-gray-700/20'
                      }`}>
                        <span className="text-white">True</span>
                        {'true' === question.correctAnswer && (
                          <Badge className="ml-2 bg-green-500/20 text-green-400">Correct</Badge>
                        )}
                      </div>
                      <div className={`p-3 rounded-lg border ${
                        'false' === question.correctAnswer 
                          ? 'border-green-500/50 bg-green-500/10' 
                          : 'border-gray-600/30 bg-gray-700/20'
                      }`}>
                        <span className="text-white">False</span>
                        {'false' === question.correctAnswer && (
                          <Badge className="ml-2 bg-green-500/20 text-green-400">Correct</Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {(question.type === 'SHORT_ANSWER' || question.type === 'ESSAY') && (
                    <div className="p-3 rounded-lg border border-gray-600/30 bg-gray-700/20">
                      <span className="text-gray-400">Expected answer: </span>
                      <span className="text-white">{question.correctAnswer}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Create Assessment</h2>
        <div className="flex gap-2">
          <Button
            onClick={() => setPreviewMode(true)}
            variant="outline"
            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !title.trim() || questions.length === 0}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Assessment'}
          </Button>
        </div>
      </div>

      {/* Assessment Settings */}
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
        <CardHeader>
          <CardTitle className="text-white">Assessment Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-white">Title *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Assessment title..."
                className="bg-black/40 border-purple-500/20 text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="passingScore" className="text-white">Passing Score (%)</Label>
              <Input
                id="passingScore"
                type="number"
                min="0"
                max="100"
                value={passingScore}
                onChange={(e) => setPassingScore(Number(e.target.value))}
                className="bg-black/40 border-purple-500/20 text-white"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-white">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Assessment description..."
              rows={3}
              className="bg-black/40 border-purple-500/20 text-white"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="timeLimit" className="text-white">Time Limit (minutes)</Label>
              <Input
                id="timeLimit"
                type="number"
                min="1"
                value={timeLimit || ''}
                onChange={(e) => setTimeLimit(e.target.value ? Number(e.target.value) : null)}
                placeholder="No limit"
                className="bg-black/40 border-purple-500/20 text-white"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="required"
                checked={isRequired}
                onCheckedChange={setIsRequired}
              />
              <Label htmlFor="required" className="text-white">Required for completion</Label>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{getTotalPoints()}</div>
              <div className="text-sm text-gray-400">Total Points</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Questions */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-white">Questions ({questions.length})</h3>
          <Button
            onClick={addQuestion}
            className="bg-purple-500 hover:bg-purple-600 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Question
          </Button>
        </div>

        <AnimatePresence>
          {questions.map((question, index) => (
            <motion.div
              key={question.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <GripVertical className="h-5 w-5 text-gray-400" />
                      <span className="font-semibold text-white">Question {index + 1}</span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => duplicateQuestion(question.id)}
                        variant="ghost"
                        size="sm"
                        className="text-gray-400 hover:text-white"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => deleteQuestion(question.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="md:col-span-2 space-y-2">
                      <Label className="text-white">Question *</Label>
                      <Textarea
                        value={question.question}
                        onChange={(e) => updateQuestion(question.id, { question: e.target.value })}
                        placeholder="Enter your question..."
                        rows={2}
                        className="bg-black/40 border-purple-500/20 text-white"
                      />
                    </div>
                    
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label className="text-white">Type</Label>
                        <Select
                          value={question.type}
                          onValueChange={(value: any) => updateQuestion(question.id, { type: value })}
                        >
                          <SelectTrigger className="bg-black/40 border-purple-500/20 text-white">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="MULTIPLE_CHOICE">Multiple Choice</SelectItem>
                            <SelectItem value="TRUE_FALSE">True/False</SelectItem>
                            <SelectItem value="SHORT_ANSWER">Short Answer</SelectItem>
                            <SelectItem value="ESSAY">Essay</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label className="text-white">Points</Label>
                        <Input
                          type="number"
                          min="1"
                          value={question.points}
                          onChange={(e) => updateQuestion(question.id, { points: Number(e.target.value) })}
                          className="bg-black/40 border-purple-500/20 text-white"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Question Type Specific Fields */}
                  {question.type === 'MULTIPLE_CHOICE' && question.options && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-white">Options</Label>
                        <Button
                          onClick={() => addOption(question.id)}
                          variant="outline"
                          size="sm"
                          className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Add Option
                        </Button>
                      </div>
                      
                      {question.options.map((option, optionIndex) => (
                        <div key={optionIndex} className="flex items-center gap-2">
                          <Input
                            value={option}
                            onChange={(e) => updateOption(question.id, optionIndex, e.target.value)}
                            className="bg-black/40 border-purple-500/20 text-white"
                          />
                          <Button
                            onClick={() => updateQuestion(question.id, { correctAnswer: option })}
                            variant={question.correctAnswer === option ? "default" : "outline"}
                            size="sm"
                            className={question.correctAnswer === option 
                              ? "bg-green-500 hover:bg-green-600 text-white" 
                              : "border-green-500/30 text-green-400 hover:bg-green-500/10"
                            }
                          >
                            {question.correctAnswer === option ? 'Correct' : 'Set Correct'}
                          </Button>
                          {question.options && question.options.length > 2 && (
                            <Button
                              onClick={() => removeOption(question.id, optionIndex)}
                              variant="ghost"
                              size="sm"
                              className="text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {question.type === 'TRUE_FALSE' && (
                    <div className="space-y-2">
                      <Label className="text-white">Correct Answer</Label>
                      <Select
                        value={question.correctAnswer}
                        onValueChange={(value) => updateQuestion(question.id, { correctAnswer: value })}
                      >
                        <SelectTrigger className="bg-black/40 border-purple-500/20 text-white">
                          <SelectValue placeholder="Select correct answer" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">True</SelectItem>
                          <SelectItem value="false">False</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {(question.type === 'SHORT_ANSWER' || question.type === 'ESSAY') && (
                    <div className="space-y-2">
                      <Label className="text-white">Expected Answer / Keywords</Label>
                      <Textarea
                        value={question.correctAnswer}
                        onChange={(e) => updateQuestion(question.id, { correctAnswer: e.target.value })}
                        placeholder="Enter expected answer or keywords for grading..."
                        rows={question.type === 'ESSAY' ? 4 : 2}
                        className="bg-black/40 border-purple-500/20 text-white"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>

        {questions.length === 0 && (
          <Card className="border-dashed border-purple-500/30 bg-black/20">
            <CardContent className="p-8 text-center">
              <Award className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No Questions Yet</h3>
              <p className="text-gray-400 mb-4">Add your first question to get started.</p>
              <Button
                onClick={addQuestion}
                className="bg-purple-500 hover:bg-purple-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Question
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Footer Actions */}
      <div className="flex justify-end gap-3">
        <Button
          onClick={onCancel}
          variant="outline"
          className="border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          disabled={isSaving || !title.trim() || questions.length === 0}
          className="bg-green-500 hover:bg-green-600 text-white"
        >
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save Assessment'}
        </Button>
      </div>
    </div>
  );
}
