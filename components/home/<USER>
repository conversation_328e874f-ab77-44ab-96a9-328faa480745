'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { AnimatedCard } from '@/components/ui/animated-card';
import { ArrowRight, TrendingUp, Users } from 'lucide-react';
import { companies } from '@/lib/data/companies';

// Transform company data for the homepage display
const projects = companies.map((company, index) => ({
  id: company.id,
  title: company.name,
  category: company.industry,
  technologies: [company.programTrack.name, company.currentStage],
  image: company.logo,
  link: `/companies/${company.id}/instructions`,
  description: company.description,
  metrics: {
    progress: `${company.progress.overall}%`,
    mentors: `${company.mentors.length}`,
    milestones: `${company.milestones.length}`
  },
  icon: company.programTrack.icon,
  featured: index < 2, // First two companies are featured
  programTrack: company.programTrack,
  joinDate: company.joinDate,
  currentStage: company.currentStage
}));

// Success Story Card Component
const SuccessStoryCard = ({ project, index }: { project: typeof projects[0]; index: number }) => {
  return (
    <AnimatedCard
      variant="glass"
      hoverEffect="lift"
      delay={index * 0.1}
      className="h-full group"
    >
      <Link href={project.link} className="h-full block">
        <div className="relative h-full flex flex-col">
          {/* Header with icon and category */}
          <div className="relative p-6 pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="text-3xl">{project.icon}</div>
              {project.featured && (
                <motion.div
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{ delay: index * 0.1 + 0.3, type: "spring" }}
                  className="px-2 py-1 bg-primary/20 text-primary text-xs font-medium rounded-full border border-primary/30"
                >
                  Featured
                </motion.div>
              )}
            </div>
            <h3 className="font-bold text-xl mb-2 group-hover:text-primary transition-colors duration-300">
              {project.title}
            </h3>
            <p className="text-primary/80 text-sm font-medium mb-3">{project.category}</p>
            <p className="text-white/70 text-sm leading-relaxed">{project.description}</p>
          </div>

          {/* Metrics section */}
          <div className="px-6 py-4 border-t border-white/10">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="flex items-center justify-center mb-1">
                  <TrendingUp className="h-3 w-3 text-green-400 mr-1" />
                  <span className="text-xs text-white/60">Progress</span>
                </div>
                <div className="text-sm font-semibold text-green-400">{project.metrics.progress}</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-1">
                  <Users className="h-3 w-3 text-blue-400 mr-1" />
                  <span className="text-xs text-white/60">Mentors</span>
                </div>
                <div className="text-sm font-semibold text-blue-400">{project.metrics.mentors}</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-1">
                  <ArrowRight className="h-3 w-3 text-purple-400 mr-1" />
                  <span className="text-xs text-white/60">Milestones</span>
                </div>
                <div className="text-sm font-semibold text-purple-400">{project.metrics.milestones}</div>
              </div>
            </div>
          </div>

          {/* Technologies footer */}
          <div className="mt-auto p-6 pt-4">
            <div className="flex flex-wrap gap-2">
              {project.technologies.map((tech) => (
                <span
                  key={tech}
                  className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium transition-colors border-primary/30 bg-primary/10 text-primary hover:bg-primary/20"
                >
                  {tech}
                </span>
              ))}
            </div>
          </div>

          {/* Hover overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-lg"
          />
        </div>
      </Link>
    </AnimatedCard>
  );
};

export default function ProjectsSection() {
  return (
    <section className="py-24 bg-gradient-to-b from-black via-black/95 to-black relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[length:50px_50px]" />
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5" />

      <div className="container mx-auto px-4 relative z-10">
        <TextReveal className="text-center max-w-4xl mx-auto mb-20">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="inline-block rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6"
          >
            Success Stories
          </motion.div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
            Transforming Ideas into Success
          </h2>
          <p className="text-white/70 text-lg leading-relaxed">
            Discover the innovative startups that have thrived with our comprehensive incubator support,
            achieving remarkable growth and making real impact in their industries.
          </p>
        </TextReveal>

        {/* Success stories grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
          {projects.map((project, index) => (
            <SuccessStoryCard key={project.id} project={project} index={index} />
          ))}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-20"
        >
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild className="group">
              <Link href="/success-stories" className="flex items-center">
                View All Success Stories
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="group">
              <Link href="/programs/portfolio" className="flex items-center">
                Portfolio Companies
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
