'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AnimatedCard } from '@/components/ui/animated-card';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { ArrowRight } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';

const services = [
  {
    titleKey: 'services.accelerator.title',
    descriptionKey: 'services.accelerator.description',
    icon: '🚀',
    link: '/programs/accelerator',
  },
  {
    titleKey: 'services.workshop.title',
    descriptionKey: 'services.workshop.description',
    icon: '💡',
    link: '/programs/workshop',
  },
  {
    titleKey: 'services.incubation.title',
    descriptionKey: 'services.incubation.description',
    icon: '🌱',
    link: '/programs/incubation',
  },
  {
    titleKey: 'services.corporate.title',
    descriptionKey: 'services.corporate.description',
    icon: '🏢',
    link: '/programs/corporate',
  },
];

export default function ServicesSection() {
  const { t } = useLanguage();
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <TextReveal className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('services.title')}
          </h2>
          <p className="text-muted-foreground text-lg">
            {t('services.subtitle')}
          </p>
        </TextReveal>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
          {services.map((service, index) => (
            <AnimatedCard key={service.titleKey} className="h-full">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <CardTitle>{t(service.titleKey)}</CardTitle>
                </CardHeader>
                <CardContent className="flex-grow">
                  <CardDescription className="text-base">{t(service.descriptionKey)}</CardDescription>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="group" asChild>
                    <Link href={service.link}>
                      {t('services.learnMore')}
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </AnimatedCard>
          ))}
        </div>

        <div className="text-center mt-16">
          <Button size="lg" asChild>
            <Link href="/programs">{t('services.exploreAll')}</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
