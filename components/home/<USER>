'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { useLanguage } from '@/lib/context/language-context';
import { TeamMemberCard } from '@/components/ui/team-member-card';

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'Founder & CEO',
    bio: 'Serial entrepreneur with 15+ years experience in tech startups',
    image: '/images/Team/team1.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Investment Director',
    bio: 'Former Google engineer with expertise in AI and machine learning',
    image: '/images/Team/team2.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Program Manager',
    bio: 'Ex-VC with $200M+ in successful investments across 30+ startups',
    image: '/images/Team/team3.jpg',
    featured: true,
  },
  {
    name: '<PERSON>',
    role: 'Operations Lead',
    bio: 'Operations expert who scaled 3 unicorns in the last decade',
    image: '/images/Team/team4.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Mentor Lead',
    bio: 'Startup advisor who has mentored 100+ founders to success',
    image: '/images/Team/team5.jpg',
    featured: false,
  },
  {
    name: '<PERSON>',
    role: 'Marketing Director',
    bio: 'Digital marketing expert with a focus on growth strategies',
    image: '/images/Team/team6.jpg',
    featured: false,
  },
];

export default function TeamSection() {
  const { t } = useLanguage();

  return (
    <section className="py-32 bg-black relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-950/5 to-black"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(147,51,234,0.08),transparent_70%)]"></div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute w-72 h-72 rounded-full bg-purple-500/5 blur-3xl"
          style={{ top: '15%', right: '15%' }}
          animate={{
            opacity: [0.3, 0.6, 0.3],
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute w-56 h-56 rounded-full bg-indigo-500/5 blur-3xl"
          style={{ bottom: '20%', left: '10%' }}
          animate={{
            opacity: [0.2, 0.5, 0.2],
            scale: [1, 1.3, 1],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="w-full px-4 sm:px-6 relative z-10">
        {/* Enhanced Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-block mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 100 }}
            transition={{ duration: 1, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="h-0.5 bg-gradient-to-r from-transparent via-purple-500 to-transparent"></div>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-purple-100 to-white bg-clip-text text-transparent leading-tight">
            {t('team.title')}
          </h2>

          <motion.p
            className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {t('team.subtitle')}
          </motion.p>

          <motion.div
            className="mt-6 h-0.5 w-20 bg-gradient-to-r from-transparent via-purple-500 to-transparent mx-auto"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            transition={{ duration: 1, delay: 0.6 }}
            viewport={{ once: true }}
          />
        </motion.div>

        {/* Enhanced Team Grid Layout */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6 w-full will-change-transform"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {teamMembers.map((member, index) => (
            <motion.div
              key={`team-member-${member.name}`}
              className="relative will-change-transform group"
              style={{
                containIntrinsicSize: '0 360px',
                contentVisibility: 'auto'
              }}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.6,
                delay: index * 0.1,
                ease: [0.22, 1, 0.36, 1]
              }}
              viewport={{ once: true, margin: "-50px" }}
              whileHover={{ y: -8 }}
            >
              {/* Enhanced Card Background */}
              <div className="absolute inset-0 bg-gradient-to-b from-purple-500/5 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl"></div>

              <TeamMemberCard
                name={member.name}
                role={member.role}
                image={member.image}
                index={index}
                className="h-[280px] md:h-[320px] lg:h-[360px] relative z-10"
              />

              {/* Hover Glow Effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/10 via-transparent to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Call to Action */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Button
              size="lg"
              asChild
              className="relative overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white border-0 px-8 py-4 text-lg font-semibold shadow-2xl shadow-purple-500/25 group"
            >
              <Link href="/team" className="flex items-center">
                <span className="relative z-10">{t('team.meetFullTeam')}</span>
                <motion.svg
                  className="ml-2 h-5 w-5 relative z-10"
                  viewBox="0 0 16 16"
                  fill="none"
                  initial={{ x: 0 }}
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  <path d="M3.33337 8H12.6667" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M8 3.33331L12.6667 7.99998L8 12.6666" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </motion.svg>

                {/* Animated background effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                  initial={{ x: "-100%" }}
                  whileHover={{ x: "100%" }}
                  transition={{ duration: 0.6 }}
                />
              </Link>
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
