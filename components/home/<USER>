'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { GrowthStages } from '@/components/ui/aceternity/growth-stages';

export default function GrowthSection() {
  return (
    <div className="py-24 lg:py-32 bg-gradient-to-b from-black via-purple-950/5 to-black relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(147,51,234,0.08),transparent_70%)]"></div>

      {/* Animated Background Elements with Smoother Transitions */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute w-96 h-96 rounded-full bg-purple-500/4 blur-3xl"
          style={{ top: '10%', right: '5%' }}
          animate={{
            opacity: [0.2, 0.5, 0.2],
            scale: [1, 1.15, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: [0.4, 0, 0.6, 1]
          }}
        />
        <motion.div
          className="absolute w-80 h-80 rounded-full bg-indigo-500/4 blur-3xl"
          style={{ bottom: '15%', left: '5%' }}
          animate={{
            opacity: [0.15, 0.4, 0.15],
            scale: [1, 1.2, 1],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 35,
            repeat: Infinity,
            ease: [0.4, 0, 0.6, 1]
          }}
        />
        <motion.div
          className="absolute w-64 h-64 rounded-full bg-pink-500/3 blur-3xl"
          style={{ top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}
          animate={{
            opacity: [0.1, 0.3, 0.1],
            scale: [1, 1.1, 1],
            rotate: [0, -180, -360],
          }}
          transition={{
            duration: 40,
            repeat: Infinity,
            ease: [0.4, 0, 0.6, 1]
          }}
        />
      </div>

      <div className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12 xl:px-16 relative z-10">
        {/* Enhanced Section Header with Professional Styling */}
        <motion.div
          className="text-center mb-24 lg:mb-28"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            className="inline-block mb-8"
            initial={{ width: 0, opacity: 0 }}
            whileInView={{ width: 120, opacity: 1 }}
            transition={{ duration: 1.2, delay: 0.3, ease: [0.16, 1, 0.3, 1] }}
            viewport={{ once: true }}
          >
            <div className="h-0.5 bg-gradient-to-r from-transparent via-purple-400 to-transparent"></div>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl lg:text-7xl xl:text-8xl font-bold mb-8 bg-gradient-to-r from-white via-purple-100 to-white bg-clip-text text-transparent leading-[1.1] tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2, ease: [0.16, 1, 0.3, 1] }}
            viewport={{ once: true }}
          >
            From Idea to{' '}
            <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 bg-clip-text text-transparent">
              Profitable Business
            </span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl lg:text-2xl text-gray-300 max-w-5xl mx-auto leading-relaxed font-light"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4, ease: [0.16, 1, 0.3, 1] }}
            viewport={{ once: true }}
          >
            We support startups at every stage of growth with tailored resources, expert mentorship, and strategic partnerships to ensure your entrepreneurial success.
          </motion.p>

          <motion.div
            className="mt-8 h-0.5 w-24 bg-gradient-to-r from-transparent via-purple-400 to-transparent mx-auto"
            initial={{ width: 0, opacity: 0 }}
            whileInView={{ width: 96, opacity: 1 }}
            transition={{ duration: 1.2, delay: 0.6, ease: [0.16, 1, 0.3, 1] }}
            viewport={{ once: true }}
          />
        </motion.div>

        {/* Enhanced GrowthStages Component with Smoother Animation */}
        <motion.div
          initial={{ opacity: 0, y: 60 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.2, delay: 0.3, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <GrowthStages className="mb-24 lg:mb-32" />
        </motion.div>

        {/* Enhanced Growth Cards with Better Spacing */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 xl:gap-16">
          {[
            {
              id: 'ideation',
              title: 'Ideation & Validation',
              description: 'Transform your idea into a viable product through market research, customer validation, and MVP development',
              icon: '🌱',
              gradient: 'from-green-500 to-emerald-500'
            },
            {
              id: 'growth',
              title: 'Growth & Scaling',
              description: 'Access resources and expertise to rapidly grow your business, optimize operations, and expand your customer base',
              icon: '🌿',
              gradient: 'from-purple-500 to-indigo-500'
            },
            {
              id: 'expansion',
              title: 'Market Expansion',
              description: 'Expand your business nationally and internationally with our strategic support and partnership network',
              icon: '🌳',
              gradient: 'from-blue-500 to-cyan-500'
            },
          ].map((item, index) => (
            <motion.div
              key={item.id}
              className="group relative"
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 1,
                delay: index * 0.15,
                ease: [0.16, 1, 0.3, 1]
              }}
              viewport={{ once: true, margin: "-80px" }}
              whileHover={{
                y: -12,
                transition: { duration: 0.4, ease: [0.16, 1, 0.3, 1] }
              }}
            >
              {/* Card Background with Enhanced Styling */}
              <div className="relative h-full p-8 lg:p-10 xl:p-12 rounded-3xl bg-gradient-to-b from-gray-900/60 to-black/60 backdrop-blur-md border border-gray-800/60 group-hover:border-purple-500/60 transition-all duration-700 ease-out">
                {/* Enhanced Gradient Border Effect */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${item.gradient} opacity-0 group-hover:opacity-25 transition-all duration-700 ease-out blur-xl`}></div>

                {/* Subtle Inner Glow */}
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-b from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

                {/* Enhanced Content */}
                <div className="relative z-10">
                  {/* Icon with Smoother Animation */}
                  <motion.div
                    className="text-6xl lg:text-7xl xl:text-8xl mb-8"
                    whileHover={{
                      scale: 1.15,
                      rotate: 8,
                      transition: { duration: 0.3, ease: [0.16, 1, 0.3, 1] }
                    }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    {item.icon}
                  </motion.div>

                  {/* Enhanced Title */}
                  <h3 className={`text-2xl lg:text-3xl xl:text-4xl font-bold mb-6 bg-gradient-to-r ${item.gradient} bg-clip-text text-transparent leading-tight`}>
                    {item.title}
                  </h3>

                  {/* Enhanced Description */}
                  <p className="text-gray-300 text-base lg:text-lg leading-relaxed font-light">
                    {item.description}
                  </p>

                  {/* Enhanced Hover Effect Line */}
                  <motion.div
                    className={`mt-8 h-0.5 bg-gradient-to-r ${item.gradient}`}
                    initial={{ width: 0, opacity: 0 }}
                    whileInView={{ width: 80, opacity: 1 }}
                    transition={{
                      duration: 1,
                      delay: index * 0.15 + 0.8,
                      ease: [0.16, 1, 0.3, 1]
                    }}
                    viewport={{ once: true }}
                  />
                </div>

                {/* Enhanced Animated Background Particles */}
                <div className="absolute inset-0 overflow-hidden rounded-3xl">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <motion.div
                      key={`growth-particle-${item.id}-${i}`}
                      className="absolute w-1 h-1 bg-purple-400/20 rounded-full"
                      style={{
                        left: Math.random() * 100 + '%',
                        top: Math.random() * 100 + '%',
                      }}
                      animate={{
                        y: [0, -30, 0],
                        x: [0, Math.random() * 20 - 10, 0],
                        opacity: [0, 0.8, 0],
                        scale: [0.5, 1, 0.5],
                      }}
                      transition={{
                        duration: 4 + Math.random() * 3,
                        repeat: Infinity,
                        delay: Math.random() * 3,
                        ease: [0.16, 1, 0.3, 1]
                      }}
                    />
                  ))}

                  {/* Additional Floating Elements */}
                  {Array.from({ length: 2 }).map((_, i) => (
                    <motion.div
                      key={`growth-orb-${item.id}-${i}`}
                      className={`absolute w-2 h-2 rounded-full bg-gradient-to-r ${item.gradient} opacity-10`}
                      style={{
                        left: Math.random() * 100 + '%',
                        top: Math.random() * 100 + '%',
                      }}
                      animate={{
                        y: [0, -40, 0],
                        opacity: [0.1, 0.3, 0.1],
                        scale: [0.8, 1.2, 0.8],
                      }}
                      transition={{
                        duration: 6 + Math.random() * 4,
                        repeat: Infinity,
                        delay: Math.random() * 4,
                        ease: [0.16, 1, 0.3, 1]
                      }}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
