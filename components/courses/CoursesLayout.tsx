'use client';

import { useEffect, useState } from "react";
import CoursesNavbar from "./CoursesNavbar";
import { LanguageProvider } from "@/lib/context/language-context";
import { ShootingStars } from "@/components/ui/shooting-stars";
import { StarsBackground } from "@/components/ui/stars-background";
import GradientCursor from "@/components/ui/gradient-cursor";

export default function CoursesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [mounted, setMounted] = useState(false);
  const [isCursorActive, setIsCursorActive] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <html lang="en" className="scroll-smooth dark">
        <body className="min-h-screen flex flex-col bg-black text-white">
          <div className="fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-md border-b border-primary/10">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="relative w-10 h-10 rounded-full overflow-hidden bg-primary/20"></div>
                <span className="font-bold text-xl">INNO<span className="text-primary">HUB</span> Courses</span>
              </div>
            </div>
          </div>
          <div className="flex-1 mt-20 flex items-center justify-center">
            <div className="animate-pulse">
              <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
              <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
              <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
            </div>
          </div>
        </body>
      </html>
    );
  }

  return (
    <html lang="en" className="scroll-smooth dark">
      <body
        className="min-h-screen flex flex-col bg-black text-white relative"
        onMouseEnter={() => setIsCursorActive(true)}
        onMouseLeave={() => setIsCursorActive(false)}
      >
        <StarsBackground />
        <ShootingStars />
        <LanguageProvider>
          <CoursesNavbar />
          <main className="flex-1 relative z-10">{children}</main>
        </LanguageProvider>
        <GradientCursor isActive={isCursorActive} />
      </body>
    </html>
  );
}
