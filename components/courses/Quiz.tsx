'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Award, 
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Flag
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import assessmentService, { AssessmentWithQuestions, QuizAnswer } from '@/lib/services/assessmentService';

interface QuizProps {
  assessmentId: string;
  onComplete?: (result: any) => void;
  onClose?: () => void;
}

export default function Quiz({ assessmentId, onComplete, onClose }: QuizProps) {
  const { data: session } = useSession();
  const [assessment, setAssessment] = useState<AssessmentWithQuestions | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [questionId: string]: string }>({});
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [startTime, setStartTime] = useState<Date>(new Date());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAssessment();
  }, [assessmentId]);

  useEffect(() => {
    if (assessment?.timeLimit && timeRemaining === null) {
      setTimeRemaining(assessment.timeLimit * 60); // Convert minutes to seconds
    }
  }, [assessment]);

  useEffect(() => {
    if (timeRemaining !== null && timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev && prev <= 1) {
            handleSubmit(); // Auto-submit when time runs out
            return 0;
          }
          return prev ? prev - 1 : 0;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [timeRemaining]);

  const loadAssessment = async () => {
    try {
      setIsLoading(true);
      const data = await assessmentService.getAssessmentById(assessmentId);
      setAssessment(data);
      setStartTime(new Date());
    } catch (error) {
      console.error('Failed to load assessment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmit = async () => {
    if (!assessment || !session?.user?.id) return;

    setIsSubmitting(true);
    try {
      const timeSpent = Math.floor((new Date().getTime() - startTime.getTime()) / 1000);
      const quizAnswers: QuizAnswer[] = assessment.questions.map(question => ({
        questionId: question.id,
        answer: answers[question.id] || ''
      }));

      const result = await assessmentService.submitQuiz({
        assessmentId: assessment.id,
        userId: session.user.id,
        answers: quizAnswers,
        timeSpent
      });

      setResult(result);
      onComplete?.(result);
    } catch (error) {
      console.error('Failed to submit quiz:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    if (!assessment) return 0;
    const answeredQuestions = Object.keys(answers).length;
    return (answeredQuestions / assessment.questions.length) * 100;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (!assessment) {
    return (
      <Card className="border-red-500/20 bg-red-500/5">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Assessment Not Found</h3>
          <p className="text-gray-400">The requested assessment could not be loaded.</p>
        </CardContent>
      </Card>
    );
  }

  if (result) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="space-y-6"
      >
        <Card className={`border-2 ${result.passed ? 'border-green-500/50 bg-green-500/5' : 'border-red-500/50 bg-red-500/5'}`}>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              {result.passed ? (
                <CheckCircle className="h-16 w-16 text-green-500" />
              ) : (
                <XCircle className="h-16 w-16 text-red-500" />
              )}
            </div>
            <CardTitle className="text-2xl text-white">
              {result.passed ? 'Congratulations!' : 'Assessment Complete'}
            </CardTitle>
            <p className="text-gray-400">
              {result.passed 
                ? 'You have successfully passed this assessment!' 
                : 'You did not meet the passing score this time.'}
            </p>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className={`text-3xl font-bold ${result.passed ? 'text-green-400' : 'text-red-400'}`}>
                  {result.score}%
                </div>
                <div className="text-gray-400">Your Score</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400">
                  {assessment.passingScore}%
                </div>
                <div className="text-gray-400">Passing Score</div>
              </div>
            </div>

            <Progress 
              value={result.score} 
              className="h-3"
            />

            {result.passed && (
              <div className="text-center">
                <Badge className="bg-green-500/20 text-green-400 px-4 py-2">
                  <Award className="h-4 w-4 mr-2" />
                  Certificate Eligible
                </Badge>
              </div>
            )}

            <div className="flex gap-3 justify-center">
              <Button
                onClick={onClose}
                variant="outline"
                className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
              >
                Continue Learning
              </Button>
              {!result.passed && (
                <Button
                  onClick={() => {
                    setResult(null);
                    setAnswers({});
                    setCurrentQuestionIndex(0);
                    setStartTime(new Date());
                    if (assessment.timeLimit) {
                      setTimeRemaining(assessment.timeLimit * 60);
                    }
                  }}
                  className="bg-purple-500 hover:bg-purple-600 text-white"
                >
                  Retake Assessment
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  const currentQuestion = assessment.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === assessment.questions.length - 1;
  const canSubmit = Object.keys(answers).length === assessment.questions.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white">{assessment.title}</CardTitle>
              <p className="text-gray-400">{assessment.description}</p>
            </div>
            {timeRemaining !== null && (
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-purple-400" />
                <span className={`font-mono text-lg ${timeRemaining < 300 ? 'text-red-400' : 'text-white'}`}>
                  {formatTime(timeRemaining)}
                </span>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-400">
              <span>Question {currentQuestionIndex + 1} of {assessment.questions.length}</span>
              <span>{Math.round(getProgressPercentage())}% Complete</span>
            </div>
            <Progress value={getProgressPercentage()} className="h-2" />
          </div>
        </CardHeader>
      </Card>

      {/* Question */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentQuestionIndex}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
            <CardContent className="p-6">
              <div className="space-y-6">
                <div className="flex items-start gap-3">
                  <Badge variant="outline" className="border-purple-500/30 text-purple-400">
                    {currentQuestion.points} {currentQuestion.points === 1 ? 'point' : 'points'}
                  </Badge>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      {currentQuestion.question}
                    </h3>

                    {/* Answer Input */}
                    {currentQuestion.type === 'MULTIPLE_CHOICE' && (
                      <RadioGroup
                        value={answers[currentQuestion.id] || ''}
                        onValueChange={(value) => handleAnswerChange(currentQuestion.id, value)}
                      >
                        {(currentQuestion.options as string[])?.map((option, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <RadioGroupItem value={option} id={`option-${index}`} />
                            <Label htmlFor={`option-${index}`} className="text-white cursor-pointer">
                              {option}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}

                    {currentQuestion.type === 'TRUE_FALSE' && (
                      <RadioGroup
                        value={answers[currentQuestion.id] || ''}
                        onValueChange={(value) => handleAnswerChange(currentQuestion.id, value)}
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="true" id="true" />
                          <Label htmlFor="true" className="text-white cursor-pointer">True</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="false" id="false" />
                          <Label htmlFor="false" className="text-white cursor-pointer">False</Label>
                        </div>
                      </RadioGroup>
                    )}

                    {currentQuestion.type === 'SHORT_ANSWER' && (
                      <Input
                        value={answers[currentQuestion.id] || ''}
                        onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                        placeholder="Enter your answer..."
                        className="bg-black/40 border-purple-500/20 text-white"
                      />
                    )}

                    {currentQuestion.type === 'ESSAY' && (
                      <Textarea
                        value={answers[currentQuestion.id] || ''}
                        onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                        placeholder="Write your essay answer..."
                        rows={6}
                        className="bg-black/40 border-purple-500/20 text-white"
                      />
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={() => setCurrentQuestionIndex(prev => Math.max(0, prev - 1))}
          disabled={currentQuestionIndex === 0}
          variant="outline"
          className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex gap-2">
          {assessment.questions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentQuestionIndex(index)}
              className={`w-8 h-8 rounded-full text-sm font-medium transition-colors ${
                index === currentQuestionIndex
                  ? 'bg-purple-500 text-white'
                  : answers[assessment.questions[index].id]
                  ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                  : 'bg-gray-700/50 text-gray-400 border border-gray-600/30'
              }`}
            >
              {index + 1}
            </button>
          ))}
        </div>

        {isLastQuestion ? (
          <Button
            onClick={handleSubmit}
            disabled={!canSubmit || isSubmitting}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Assessment'}
            <Flag className="h-4 w-4 ml-2" />
          </Button>
        ) : (
          <Button
            onClick={() => setCurrentQuestionIndex(prev => Math.min(assessment.questions.length - 1, prev + 1))}
            className="bg-purple-500 hover:bg-purple-600 text-white"
          >
            Next
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        )}
      </div>
    </div>
  );
}
