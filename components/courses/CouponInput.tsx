'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Check, 
  X, 
  Loader2, 
  Tag, 
  Percent, 
  Gift,
  AlertCircle,
  Sparkles
} from 'lucide-react';
import couponService from '@/lib/services/couponService';
import { CouponValidationResult, COUPON_ERROR_MESSAGES } from '@/lib/types/coupon';
import { getCurrentUser } from '@/lib/auth';

interface CouponInputProps {
  courseId?: string;
  coursePrice?: number;
  onCouponApplied?: (result: CouponValidationResult) => void;
  onCouponRemoved?: () => void;
  className?: string;
}

export default function CouponInput({ 
  courseId, 
  coursePrice = 0, 
  onCouponApplied, 
  onCouponRemoved,
  className = '' 
}: CouponInputProps) {
  const [couponCode, setCouponCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<CouponValidationResult | null>(null);
  const [appliedCoupon, setAppliedCoupon] = useState<CouponValidationResult | null>(null);
  const [showInput, setShowInput] = useState(false);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const currentUser = getCurrentUser();
    setUser(currentUser);
  }, []);

  const validateCoupon = async (code: string) => {
    if (!code.trim() || !user) return;

    setIsValidating(true);
    setValidationResult(null);

    try {
      const result = await couponService.validateCoupon({
        couponCode: code,
        userId: user.id,
        courseId,
        coursePrice,
      });

      setValidationResult(result);
    } catch (error) {
      console.error('Coupon validation error:', error);
      setValidationResult({
        isValid: false,
        error: 'COUPON_NOT_FOUND'
      });
    } finally {
      setIsValidating(false);
    }
  };

  const applyCoupon = async () => {
    if (!validationResult?.isValid || !user) return;

    try {
      const redemption = await couponService.applyCoupon({
        couponCode: couponCode,
        userId: user.id,
        courseId,
        coursePrice,
      });

      if (redemption) {
        setAppliedCoupon(validationResult);
        setShowInput(false);
        setCouponCode('');
        setValidationResult(null);
        onCouponApplied?.(validationResult);
      }
    } catch (error) {
      console.error('Coupon application error:', error);
      setValidationResult({
        isValid: false,
        error: 'COUPON_NOT_FOUND'
      });
    }
  };

  const removeCoupon = () => {
    setAppliedCoupon(null);
    onCouponRemoved?.();
  };

  const handleInputChange = (value: string) => {
    setCouponCode(value.toUpperCase());
    if (value.length >= 3) {
      validateCoupon(value);
    } else {
      setValidationResult(null);
    }
  };

  const getDiscountText = (result: CouponValidationResult) => {
    if (!result.coupon) return '';
    
    if (result.coupon.discountType === 'free_access') {
      return 'FREE ACCESS';
    } else {
      return `${result.coupon.discountValue}% OFF`;
    }
  };

  const getSavingsText = (result: CouponValidationResult) => {
    if (!result.discountAmount) return '';
    return `Save $${result.discountAmount}`;
  };

  if (appliedCoupon) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`${className}`}
      >
        <Card className="border-green-500/30 bg-green-500/10">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                  <Gift className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-green-400">
                      {appliedCoupon.coupon?.code}
                    </span>
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <Check className="h-4 w-4 text-green-400" />
                    </motion.div>
                  </div>
                  <div className="text-sm text-gray-400">
                    {getDiscountText(appliedCoupon)} • {getSavingsText(appliedCoupon)}
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={removeCoupon}
                className="text-gray-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <div className={`${className}`}>
      {!showInput ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <Button
            variant="outline"
            onClick={() => setShowInput(true)}
            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10 flex items-center gap-2"
          >
            <Tag className="h-4 w-4" />
            Have a coupon?
          </Button>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-4"
        >
          <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
            <CardContent className="p-4">
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-3">
                  <Tag className="h-5 w-5 text-purple-400" />
                  <span className="font-medium text-white">Enter Coupon Code</span>
                </div>

                <div className="flex gap-2">
                  <div className="flex-1 relative">
                    <Input
                      value={couponCode}
                      onChange={(e) => handleInputChange(e.target.value)}
                      placeholder="Enter coupon code (e.g., WELCOME2024)"
                      className="bg-black/40 border-purple-500/20 text-white placeholder-gray-400 uppercase"
                      disabled={isValidating}
                    />
                    
                    {/* Validation indicator */}
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <AnimatePresence>
                        {isValidating && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0 }}
                          >
                            <Loader2 className="h-4 w-4 text-purple-400 animate-spin" />
                          </motion.div>
                        )}
                        {validationResult?.isValid && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0 }}
                          >
                            <Check className="h-4 w-4 text-green-400" />
                          </motion.div>
                        )}
                        {validationResult && !validationResult.isValid && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0 }}
                          >
                            <X className="h-4 w-4 text-red-400" />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>

                  <Button
                    onClick={applyCoupon}
                    disabled={!validationResult?.isValid || isValidating}
                    className="bg-purple-500 hover:bg-purple-600 text-white"
                  >
                    Apply
                  </Button>
                </div>

                {/* Validation feedback */}
                <AnimatePresence>
                  {validationResult && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="mt-3"
                    >
                      {validationResult.isValid ? (
                        <div className="flex items-center gap-2 p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                          <Sparkles className="h-4 w-4 text-green-400" />
                          <div className="flex-1">
                            <div className="text-green-400 font-medium">
                              {getDiscountText(validationResult)}
                            </div>
                            <div className="text-sm text-gray-400">
                              {validationResult.coupon?.description}
                            </div>
                            {validationResult.discountAmount && validationResult.discountAmount > 0 && (
                              <div className="text-sm text-green-400 mt-1">
                                You'll save ${validationResult.discountAmount}!
                              </div>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 p-3 rounded-lg bg-red-500/10 border border-red-500/20">
                          <AlertCircle className="h-4 w-4 text-red-400" />
                          <div className="text-red-400 text-sm">
                            {COUPON_ERROR_MESSAGES[validationResult.error as keyof typeof COUPON_ERROR_MESSAGES]}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>

                <div className="flex justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowInput(false);
                      setCouponCode('');
                      setValidationResult(null);
                    }}
                    className="text-gray-400 hover:text-white"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
