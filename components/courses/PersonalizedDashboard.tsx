'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/lib/context/language-context';
import { getCurrentUser } from '@/lib/auth';
import { 
  TrendingUp, 
  BookOpen, 
  Award, 
  Target, 
  Brain,
  Lightbulb,
  Users,
  Star,
  Clock,
  PlayCircle
} from 'lucide-react';

interface TalentScores {
  analytical: number;
  creative: number;
  social: number;
  strategic: number;
}

interface RecommendedCourse {
  id: number;
  title: string;
  description: string;
  duration: string;
  level: string;
  matchPercentage: number;
  category: string;
  videoId: string;
}

const talentIcons = {
  analytical: <Brain className="h-5 w-5" />,
  creative: <Lightbulb className="h-5 w-5" />,
  social: <Users className="h-5 w-5" />,
  strategic: <Target className="h-5 w-5" />
};

const talentColors = {
  analytical: 'text-blue-400',
  creative: 'text-purple-400',
  social: 'text-green-400',
  strategic: 'text-orange-400'
};

export default function PersonalizedDashboard() {
  const { t, isLoaded } = useLanguage();
  const [user, setUser] = useState<any>(null);
  const [primaryTalent, setPrimaryTalent] = useState<string>('');
  const [recommendedCourses, setRecommendedCourses] = useState<RecommendedCourse[]>([]);

  useEffect(() => {
    const currentUser = getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
      
      // Get assessment results
      const assessmentResults = localStorage.getItem('assessmentResults');
      if (assessmentResults) {
        try {
          const scores: TalentScores = JSON.parse(assessmentResults);
          
          // Find primary talent
          const sortedTalents = Object.entries(scores)
            .sort(([,a], [,b]) => b - a);
          
          setPrimaryTalent(sortedTalents[0][0]);
          
          // Generate personalized course recommendations
          generateRecommendations(sortedTalents[0][0], scores);
        } catch (error) {
          console.error('Error parsing assessment results:', error);
        }
      }
    }
  }, []);

  const generateRecommendations = (primaryTalent: string, scores: TalentScores) => {
    // Enhanced course recommendations based on talent assessment
    const allCourses = [
      {
        id: 1,
        title: 'Entrepreneurship Fundamentals',
        description: 'Learn the fundamentals of starting and growing a successful business',
        duration: '1h 29m',
        level: 'Beginner',
        category: 'Business',
        talents: ['strategic', 'analytical'],
        videoId: 'hlCYcATi0m4'
      },
      {
        id: 2,
        title: 'Innovation Hub Insights',
        description: 'Discover how innovation hubs work and their role in startup ecosystem',
        duration: '45m',
        level: 'Beginner',
        category: 'Business',
        talents: ['creative', 'social'],
        videoId: 'esjWT-D9OF8'
      },
      {
        id: 3,
        title: 'Startup Funding Masterclass',
        description: 'Complete guide to raising capital and securing investment for your startup',
        duration: '18m',
        level: 'Intermediate',
        category: 'Finance',
        talents: ['analytical', 'strategic'],
        videoId: 'dQw4w9WgXcQ'
      },
      {
        id: 4,
        title: 'Building Successful Teams',
        description: 'Learn how to recruit, manage, and scale high-performing startup teams',
        duration: '15m',
        level: 'Intermediate',
        category: 'Business',
        talents: ['social', 'strategic'],
        videoId: 'jNQXAC9IVRw'
      },
      {
        id: 5,
        title: 'Digital Marketing for Startups',
        description: 'Master digital marketing strategies to grow your startup on a budget',
        duration: '22m',
        level: 'Beginner',
        category: 'Marketing',
        talents: ['creative', 'social'],
        videoId: 'gElfIo6uw4g'
      },
      {
        id: 6,
        title: 'Product Development Strategy',
        description: 'From idea to market: comprehensive product development methodology',
        duration: '25m',
        level: 'Advanced',
        category: 'Product',
        talents: ['creative', 'analytical'],
        videoId: 'M966MmhOBT0'
      },
      {
        id: 7,
        title: 'Scaling Your Business',
        description: 'Strategies and frameworks for scaling your startup to the next level',
        duration: '20m',
        level: 'Advanced',
        category: 'Business',
        talents: ['strategic', 'analytical'],
        videoId: 'hlCYcATi0m4'
      },
      {
        id: 8,
        title: 'Leadership in Startups',
        description: 'Develop essential leadership skills for startup founders and executives',
        duration: '30m',
        level: 'Intermediate',
        category: 'Business',
        talents: ['social', 'strategic'],
        videoId: 'esjWT-D9OF8'
      }
    ];

    // Calculate match percentages based on user's talent scores
    const recommendations = allCourses.map(course => {
      let matchScore = 0;
      course.talents.forEach(talent => {
        matchScore += scores[talent as keyof TalentScores] || 0;
      });
      
      const matchPercentage = Math.min(95, Math.max(60, (matchScore / course.talents.length) * 10));
      
      return {
        ...course,
        matchPercentage: Math.round(matchPercentage)
      };
    }).sort((a, b) => b.matchPercentage - a.matchPercentage);

    setRecommendedCourses(recommendations.slice(0, 4));
  };

  if (!isLoaded || !user) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-32 bg-gray-700/50 rounded-lg"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-48 bg-gray-700/50 rounded-lg"></div>
          <div className="h-48 bg-gray-700/50 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold mb-2">
                  {t('courses.dashboard.welcome') || `Welcome back, ${user.name}!`}
                </h1>
                <p className="text-muted-foreground">
                  {t('courses.dashboard.subtitle') || 'Continue your entrepreneurial journey with personalized recommendations'}
                </p>
              </div>
              <div className="flex items-center gap-4">
                {primaryTalent && (
                  <div className="flex items-center gap-2 px-3 py-2 bg-primary/10 rounded-lg">
                    <div className={talentColors[primaryTalent as keyof typeof talentColors]}>
                      {talentIcons[primaryTalent as keyof typeof talentIcons]}
                    </div>
                    <span className="text-sm font-medium">
                      {t(`talent.${primaryTalent}`) || primaryTalent}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Learning Progress */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <Card className="border-primary/20 bg-black/60 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              {t('courses.dashboard.progress') || 'Your Learning Progress'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">3</div>
                <div className="text-sm text-muted-foreground">
                  {t('courses.dashboard.coursesStarted') || 'Courses Started'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400 mb-1">1</div>
                <div className="text-sm text-muted-foreground">
                  {t('courses.dashboard.coursesCompleted') || 'Courses Completed'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-400 mb-1">12h</div>
                <div className="text-sm text-muted-foreground">
                  {t('courses.dashboard.totalTime') || 'Total Learning Time'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Recommended Courses */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {t('courses.dashboard.recommended') || 'Recommended for You'}
          </h2>
          <Button variant="outline" size="sm" className="border-primary/20">
            {t('courses.dashboard.viewAll') || 'View All'}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {recommendedCourses.map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
            >
              <Card className="border-primary/20 bg-black/60 backdrop-blur-md hover:border-primary/40 transition-all cursor-pointer overflow-hidden">
                {/* Course Thumbnail */}
                <div className="relative aspect-video overflow-hidden">
                  <Image
                    src={`https://img.youtube.com/vi/${course.videoId}/maxresdefault.jpg`}
                    alt={course.title}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      // Fallback for missing YouTube thumbnails
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://placehold.co/600x400/3a0647/ffffff?text=InnoHub+Course';
                    }}
                  />
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                    <PlayCircle className="h-12 w-12 text-white" />
                  </div>
                  <div className="absolute top-2 right-2 flex items-center gap-1 px-2 py-1 bg-primary/90 rounded-full">
                    <Star className="h-3 w-3 text-white" />
                    <span className="text-xs font-medium text-white">{course.matchPercentage}%</span>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="font-semibold text-lg mb-2">{course.title}</h3>
                    <p className="text-muted-foreground text-sm">
                      {course.description}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{course.duration}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Award className="h-3 w-3" />
                        <span>{course.level}</span>
                      </div>
                    </div>
                    
                    <Button size="sm" className="bg-primary hover:bg-primary/90">
                      <PlayCircle className="h-4 w-4 mr-2" />
                      {t('courses.dashboard.start') || 'Start'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}
