'use client';

import dynamic from 'next/dynamic';
import { Suspense } from 'react';
import { AnimatedGradientBackground } from '@/components/ui/animated-gradient-background';

// Import the Contact page component with SSR disabled
const ContactPageContent = dynamic(() => import('./ContactPageContent'), { ssr: false });

export default function ClientContactPage() {
  return (
    <Suspense fallback={<ContactPageFallback />}>
      <ContactPageContent />
    </Suspense>
  );
}

function ContactPageFallback() {
  return (
    <>
      <AnimatedGradientBackground className="min-h-[40vh] flex flex-col items-center justify-center pt-32 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-4">
              Get in Touch
            </div>
            
            <div className="h-12 w-3/4 bg-gray-800 rounded-lg mx-auto mb-6"></div>
            <div className="h-6 w-full bg-gray-800 rounded-lg mx-auto"></div>
          </div>
        </div>
      </AnimatedGradientBackground>
      
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <div className="h-8 w-64 bg-gray-800 rounded-lg mb-6"></div>
              <div className="h-4 w-full bg-gray-800 rounded-lg mb-8"></div>
              
              <div className="space-y-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-start space-x-4">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <div className="h-6 w-6 bg-primary rounded-full"></div>
                    </div>
                    <div>
                      <div className="h-4 w-16 bg-gray-800 rounded-lg mb-2"></div>
                      <div className="h-4 w-32 bg-gray-800 rounded-lg"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <div className="p-8 border border-border rounded-xl shadow-lg">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[1, 2].map((i) => (
                      <div key={i} className="space-y-2">
                        <div className="h-4 w-16 bg-gray-800 rounded-lg"></div>
                        <div className="h-10 w-full bg-gray-800 rounded-lg"></div>
                      </div>
                    ))}
                  </div>
                  
                  {[1, 2].map((i) => (
                    <div key={i} className="space-y-2">
                      <div className="h-4 w-16 bg-gray-800 rounded-lg"></div>
                      <div className="h-10 w-full bg-gray-800 rounded-lg"></div>
                    </div>
                  ))}
                  
                  <div className="flex justify-center">
                    <div className="h-12 w-40 bg-primary/30 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
