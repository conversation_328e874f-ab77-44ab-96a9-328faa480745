'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { Badge } from '@/components/ui/badge';
import { Cpu, Database, Cloud, Zap, Shield, Code } from 'lucide-react';

interface TechnologyInnovationProps {
  company: Company;
}

export function TechnologyInnovation({ company }: TechnologyInnovationProps) {
  const techStack = [
    { category: 'IoT Hardware', items: ['Arduino-based sensors', 'LoRaWAN connectivity', 'Solar-powered units'], icon: <Cpu className="h-5 w-5" /> },
    { category: 'Backend', items: ['Node.js', 'MongoDB', 'Redis'], icon: <Database className="h-5 w-5" /> },
    { category: 'Cloud Infrastructure', items: ['AWS IoT Core', 'Lambda Functions', 'CloudWatch'], icon: <Cloud className="h-5 w-5" /> },
    { category: 'AI/ML', items: ['TensorFlow', 'Python', 'Time Series Analysis'], icon: <Zap className="h-5 w-5" /> },
    { category: 'Security', items: ['End-to-end encryption', 'OAuth 2.0', 'Device certificates'], icon: <Shield className="h-5 w-5" /> },
    { category: 'Frontend', items: ['React Native', 'React.js', 'TypeScript'], icon: <Code className="h-5 w-5" /> }
  ];

  return (
    <div className="space-y-6">
      {/* Technology Stack */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/20 rounded-lg">
                <Cpu className="h-5 w-5 text-primary" />
              </div>
              <AuthCardTitle>Technology Stack</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {techStack.map((category, index) => (
                <motion.div
                  key={index}
                  className="p-4 bg-primary/5 border border-primary/20 rounded-lg"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="flex items-center gap-2 mb-3">
                    <div className="text-primary">{category.icon}</div>
                    <h4 className="font-medium text-white">{category.category}</h4>
                  </div>
                  <div className="space-y-1">
                    {category.items.map((item, itemIndex) => (
                      <Badge
                        key={itemIndex}
                        variant="secondary"
                        className="mr-1 mb-1 text-xs bg-primary/10 text-primary border-primary/20"
                      >
                        {item}
                      </Badge>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Innovation Highlights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Innovation Highlights</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-white mb-3">Key Innovations</h4>
                <div className="space-y-3">
                  {[
                    'AI-powered crop health prediction algorithms',
                    'Weather-adaptive irrigation scheduling',
                    'Multi-sensor data fusion technology',
                    'Edge computing for real-time decisions'
                  ].map((innovation, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                      <p className="text-sm text-muted-foreground">{innovation}</p>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-white mb-3">R&D Initiatives</h4>
                <div className="space-y-3">
                  {[
                    'Drone integration for aerial monitoring',
                    'Blockchain for supply chain tracking',
                    'Computer vision for pest detection',
                    'Predictive analytics for yield forecasting'
                  ].map((initiative, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full" />
                      <p className="text-sm text-muted-foreground">{initiative}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Intellectual Property */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Intellectual Property</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-500/5 border border-blue-500/20 rounded-lg">
                <p className="text-2xl font-bold text-blue-400 mb-1">2</p>
                <p className="text-sm text-muted-foreground">Patents Pending</p>
              </div>
              <div className="text-center p-4 bg-green-500/5 border border-green-500/20 rounded-lg">
                <p className="text-2xl font-bold text-green-400 mb-1">5</p>
                <p className="text-sm text-muted-foreground">Trade Secrets</p>
              </div>
              <div className="text-center p-4 bg-purple-500/5 border border-purple-500/20 rounded-lg">
                <p className="text-2xl font-bold text-purple-400 mb-1">3</p>
                <p className="text-sm text-muted-foreground">Trademarks</p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
