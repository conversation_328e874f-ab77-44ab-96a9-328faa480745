'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Company, FounderProfile } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { Badge } from '@/components/ui/badge';
import { AuthButton } from '@/components/ui/auth-button';
import { Linkedin, Mail, GraduationCap, Briefcase, Star } from 'lucide-react';

interface FoundersLeadershipProps {
  company: Company;
}

function PersonCard({ person, index }: { person: FounderProfile; index: number }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <AuthCard className="h-full group hover:border-primary/40 transition-colors duration-300">
        <AuthCardContent className="p-6">
          {/* Header */}
          <div className="flex items-start gap-4 mb-4">
            <div className="relative">
              <div className="w-20 h-20 rounded-full overflow-hidden bg-primary/20 border border-primary/30">
                <Image
                  src={person.photo}
                  alt={person.name}
                  width={80}
                  height={80}
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `<div class="w-full h-full flex items-center justify-center text-primary font-semibold text-lg">${person.name.split(' ').map(n => n[0]).join('')}</div>`;
                    }
                  }}
                />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-primary rounded-full border-2 border-black flex items-center justify-center">
                <Star className="h-3 w-3 text-white" />
              </div>
            </div>

            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-1">{person.name}</h3>
              <p className="text-primary text-sm font-medium mb-2">{person.role}</p>
              
              {/* Expertise */}
              <div className="flex flex-wrap gap-1">
                {person.expertise.slice(0, 3).map((skill, skillIndex) => (
                  <Badge
                    key={skillIndex}
                    variant="secondary"
                    className="text-xs bg-primary/10 text-primary border-primary/20"
                  >
                    {skill}
                  </Badge>
                ))}
                {person.expertise.length > 3 && (
                  <Badge
                    variant="secondary"
                    className="text-xs bg-muted/20 text-muted-foreground border-muted/20"
                  >
                    +{person.expertise.length - 3}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Bio */}
          <div className="mb-4">
            <p className="text-sm text-muted-foreground leading-relaxed">
              {person.bio}
            </p>
          </div>

          {/* Education */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <GraduationCap className="h-4 w-4 text-blue-400" />
              <span className="text-sm font-medium text-white">Education</span>
            </div>
            <div className="space-y-1">
              {person.education.map((edu, eduIndex) => (
                <p key={eduIndex} className="text-xs text-muted-foreground pl-6">
                  • {edu}
                </p>
              ))}
            </div>
          </div>

          {/* Experience */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Briefcase className="h-4 w-4 text-green-400" />
              <span className="text-sm font-medium text-white">Experience</span>
            </div>
            <div className="space-y-1">
              {person.previousExperience.slice(0, 3).map((exp, expIndex) => (
                <p key={expIndex} className="text-xs text-muted-foreground pl-6">
                  • {exp}
                </p>
              ))}
            </div>
          </div>

          {/* Contact */}
          <div className="flex gap-2">
            {person.linkedin && (
              <AuthButton
                variant="outline"
                size="sm"
                className="flex-1 justify-center"
                onClick={() => window.open(person.linkedin, '_blank')}
              >
                <Linkedin className="h-3 w-3 mr-1" />
                LinkedIn
              </AuthButton>
            )}
            <AuthButton
              variant="outline"
              size="sm"
              className="flex-1 justify-center"
              onClick={() => window.open(`mailto:${person.name.toLowerCase().replace(' ', '.')}@${company.website?.replace('https://', '') || 'company.com'}`, '_blank')}
            >
              <Mail className="h-3 w-3 mr-1" />
              Email
            </AuthButton>
          </div>
        </AuthCardContent>
      </AuthCard>
    </motion.div>
  );
}

export function FoundersLeadership({ company }: FoundersLeadershipProps) {
  if (!company.profile?.foundersAndLeadership) {
    return (
      <AuthCard>
        <AuthCardContent className="p-8 text-center">
          <p className="text-muted-foreground">Founders and leadership information is being prepared.</p>
        </AuthCardContent>
      </AuthCard>
    );
  }

  const { foundersAndLeadership } = company.profile;

  return (
    <div className="space-y-8">
      {/* Founders Section */}
      {foundersAndLeadership.founders && foundersAndLeadership.founders.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">Founders</h2>
            <p className="text-muted-foreground">
              Meet the visionary founders who started {company.name} and continue to drive its mission forward.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {foundersAndLeadership.founders.map((founder, index) => (
              <PersonCard key={founder.id} person={founder} index={index} />
            ))}
          </div>
        </motion.div>
      )}

      {/* Leadership Team */}
      {foundersAndLeadership.leadership && foundersAndLeadership.leadership.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">Leadership Team</h2>
            <p className="text-muted-foreground">
              Experienced leaders who bring diverse expertise and guide the company's strategic direction.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {foundersAndLeadership.leadership.map((leader, index) => (
              <PersonCard key={leader.id} person={leader} index={index} />
            ))}
          </div>
        </motion.div>
      )}

      {/* Advisory Board */}
      {foundersAndLeadership.advisors && foundersAndLeadership.advisors.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">Advisory Board</h2>
            <p className="text-muted-foreground">
              Industry experts and thought leaders who provide strategic guidance and mentorship.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {foundersAndLeadership.advisors.map((advisor, index) => (
              <PersonCard key={advisor.id} person={advisor} index={index} />
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
}
