'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { Calendar, Rocket, DollarSign, Trophy, Newspaper } from 'lucide-react';

interface CompanyTimelineProps {
  company: Company;
}

export function CompanyTimeline({ company }: CompanyTimelineProps) {
  const timelineEvents = [
    { date: '2023-01', title: 'Company Founded', description: 'EcoGrow officially founded by Batbayar and Oyunaa', type: 'founding', icon: '🚀' },
    { date: '2023-03', title: 'First Prototype', description: 'Completed initial IoT sensor prototype and testing', type: 'product', icon: '🔧' },
    { date: '2023-06', title: 'Pilot Program', description: 'Launched pilot program with 5 local farms', type: 'milestone', icon: '🌱' },
    { date: '2023-09', title: 'Pre-seed Funding', description: 'Raised $100K in pre-seed funding', type: 'funding', icon: '💰' },
    { date: '2024-01', title: 'InnoHub Accelerator', description: 'Accepted into InnoHub Accelerator Program', type: 'milestone', icon: '🎯' },
    { date: '2024-03', title: 'Product Launch', description: 'Official product launch with 20 commercial customers', type: 'product', icon: '🚀' },
    { date: '2024-06', title: 'Seed Funding Round', description: 'Currently raising $500K seed round', type: 'funding', icon: '💵' }
  ];

  const getIcon = (type: string) => {
    switch (type) {
      case 'founding': return <Rocket className="h-4 w-4" />;
      case 'product': return <Rocket className="h-4 w-4" />;
      case 'funding': return <DollarSign className="h-4 w-4" />;
      case 'milestone': return <Trophy className="h-4 w-4" />;
      case 'award': return <Trophy className="h-4 w-4" />;
      case 'media': return <Newspaper className="h-4 w-4" />;
      default: return <Calendar className="h-4 w-4" />;
    }
  };

  const getColor = (type: string) => {
    switch (type) {
      case 'founding': return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      case 'product': return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'funding': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'milestone': return 'text-purple-400 bg-purple-500/20 border-purple-500/30';
      case 'award': return 'text-orange-400 bg-orange-500/20 border-orange-500/30';
      case 'media': return 'text-pink-400 bg-pink-500/20 border-pink-500/30';
      default: return 'text-primary bg-primary/20 border-primary/30';
    }
  };

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/20 rounded-lg">
                <Calendar className="h-5 w-5 text-primary" />
              </div>
              <AuthCardTitle>Company Timeline</AuthCardTitle>
            </div>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground mb-6">
              Key milestones and achievements in {company.name}'s journey from inception to present.
            </p>
            
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-6 top-0 bottom-0 w-px bg-gradient-to-b from-primary/50 to-transparent" />
              
              <div className="space-y-6">
                {timelineEvents.map((event, index) => (
                  <motion.div
                    key={index}
                    className="relative flex items-start gap-4"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    {/* Timeline dot */}
                    <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full border ${getColor(event.type)}`}>
                      {getIcon(event.type)}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-white">{event.title}</h3>
                        <span className="text-xs text-muted-foreground bg-muted/20 px-2 py-1 rounded">
                          {new Date(event.date).toLocaleDateString('en-US', { year: 'numeric', month: 'short' })}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">{event.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
