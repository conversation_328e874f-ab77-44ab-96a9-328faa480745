'use client';

import { motion } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { Auth<PERSON>ard, Auth<PERSON>ard<PERSON>ontent, AuthCard<PERSON>eader, AuthCardTitle } from '@/components/ui/auth-card';
import { AuthButton } from '@/components/ui/auth-button';
import { BookOpen, FileText, Wrench, ExternalLink, Download, Play } from 'lucide-react';

interface ResourcesSectionProps {
  resources: Company['resources'];
}

export function ResourcesSection({ resources }: ResourcesSectionProps) {
  const resourceCategories = [
    {
      id: 'courses',
      title: 'Courses & Learning',
      description: 'Access curated courses designed for your program track',
      icon: <BookOpen className="h-6 w-6" />,
      items: resources.courses,
      color: 'from-blue-500 to-cyan-500',
      actionIcon: <Play className="h-4 w-4" />,
      actionText: 'Start Course'
    },
    {
      id: 'documents',
      title: 'Documents & Templates',
      description: 'Essential documents, templates, and guides for your journey',
      icon: <FileText className="h-6 w-6" />,
      items: resources.documents,
      color: 'from-green-500 to-emerald-500',
      actionIcon: <Download className="h-4 w-4" />,
      actionText: 'Download'
    },
    {
      id: 'tools',
      title: 'Tools & Platforms',
      description: 'Access to premium tools and platforms for your startup',
      icon: <Wrench className="h-6 w-6" />,
      items: resources.tools,
      color: 'from-purple-500 to-pink-500',
      actionIcon: <ExternalLink className="h-4 w-4" />,
      actionText: 'Access Tool'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle className="text-2xl">Resources & Tools</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground">
              Access your curated collection of courses, documents, and tools designed to accelerate your startup's growth. 
              All resources are tailored to your program track and current stage.
            </p>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Resource Categories */}
      <div className="space-y-8">
        {resourceCategories.map((category, categoryIndex) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}
          >
            <AuthCard>
              <AuthCardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${category.color} bg-opacity-20`}>
                    <div className="text-white">{category.icon}</div>
                  </div>
                  <div>
                    <AuthCardTitle className="text-xl">{category.title}</AuthCardTitle>
                    <p className="text-sm text-muted-foreground mt-1">{category.description}</p>
                  </div>
                </div>
              </AuthCardHeader>
              <AuthCardContent>
                {category.items.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {category.items.map((item, itemIndex) => (
                      <motion.div
                        key={itemIndex}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: (categoryIndex * 0.1) + (itemIndex * 0.05) }}
                        className="group"
                      >
                        <div className="bg-black/40 border border-primary/20 rounded-lg p-4 hover:border-primary/40 transition-colors duration-300 h-full flex flex-col">
                          <div className="flex-1">
                            <h4 className="font-medium text-white mb-2 group-hover:text-primary transition-colors duration-300">
                              {item}
                            </h4>
                            <p className="text-sm text-muted-foreground mb-3">
                              {category.id === 'courses' && 'Interactive course with practical exercises'}
                              {category.id === 'documents' && 'Template and guide for your startup'}
                              {category.id === 'tools' && 'Premium access included in your program'}
                            </p>
                          </div>
                          <AuthButton
                            variant="outline"
                            size="sm"
                            className="w-full justify-center group-hover:border-primary/50 transition-colors duration-300"
                          >
                            {category.actionIcon}
                            <span className="ml-2">{category.actionText}</span>
                          </AuthButton>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-muted-foreground mb-2">No {category.title.toLowerCase()} available yet</div>
                    <p className="text-sm text-muted-foreground">
                      Resources will be added as you progress through your program
                    </p>
                  </div>
                )}
              </AuthCardContent>
            </AuthCard>
          </motion.div>
        ))}
      </div>

      {/* Quick Access Panel */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Quick Access</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <AuthButton variant="outline" className="justify-start h-auto py-3">
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-2 mb-1">
                    <BookOpen className="h-4 w-4" />
                    <span className="font-medium">Course Dashboard</span>
                  </div>
                  <span className="text-xs text-muted-foreground">View all courses</span>
                </div>
              </AuthButton>
              
              <AuthButton variant="outline" className="justify-start h-auto py-3">
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-2 mb-1">
                    <FileText className="h-4 w-4" />
                    <span className="font-medium">Document Library</span>
                  </div>
                  <span className="text-xs text-muted-foreground">Browse templates</span>
                </div>
              </AuthButton>
              
              <AuthButton variant="outline" className="justify-start h-auto py-3">
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-2 mb-1">
                    <Wrench className="h-4 w-4" />
                    <span className="font-medium">Tool Access</span>
                  </div>
                  <span className="text-xs text-muted-foreground">Manage subscriptions</span>
                </div>
              </AuthButton>
              
              <AuthButton variant="outline" className="justify-start h-auto py-3">
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-2 mb-1">
                    <ExternalLink className="h-4 w-4" />
                    <span className="font-medium">External Links</span>
                  </div>
                  <span className="text-xs text-muted-foreground">Useful resources</span>
                </div>
              </AuthButton>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Resource Tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Making the Most of Your Resources</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-sm font-medium text-white mb-2">📚 Learning Path</h4>
                <p className="text-sm text-muted-foreground">
                  Follow the recommended course sequence for your program track to maximize learning outcomes.
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-white mb-2">📋 Templates</h4>
                <p className="text-sm text-muted-foreground">
                  Use our proven templates to save time and ensure you're following best practices.
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-white mb-2">🛠️ Tools</h4>
                <p className="text-sm text-muted-foreground">
                  Take advantage of premium tool access - these subscriptions are included in your program.
                </p>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
