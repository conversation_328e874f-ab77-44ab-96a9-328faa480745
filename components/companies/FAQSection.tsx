'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { AuthButton } from '@/components/ui/auth-button';
import { ChevronDown, ChevronUp, MessageCircle, Mail, Phone, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface FAQSectionProps {
  faq: Company['faq'];
}

export function FAQSection({ faq }: FAQSectionProps) {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  const filteredFAQ = faq.filter(item =>
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // General FAQ items that apply to all companies
  const generalFAQ = [
    {
      question: 'How do I access the InnoHub platform?',
      answer: 'You can access the platform through your personalized dashboard. Use the login credentials provided in your welcome email. If you need help, contact your mentor or the support team.'
    },
    {
      question: 'What is the typical schedule for mentor meetings?',
      answer: 'Mentor meetings are typically scheduled bi-weekly, but frequency can be adjusted based on your needs and program stage. You can book sessions directly through your mentor\'s Calendly link or request additional meetings as needed.'
    },
    {
      question: 'How can I connect with other startups in the program?',
      answer: 'Join our Slack workspace where you can connect with other companies, participate in discussions, and attend virtual events. We also host monthly networking sessions and peer learning workshops.'
    },
    {
      question: 'What happens if I miss a milestone deadline?',
      answer: 'Don\'t worry! Reach out to your mentor immediately to discuss the situation. We can help you create a revised timeline and provide additional support to get back on track. The key is communication.'
    },
    {
      question: 'How do I access premium tools and software?',
      answer: 'Premium tool access is included in your program. Check your resources section for available tools and access instructions. If you need a specific tool not listed, contact your mentor to discuss options.'
    }
  ];

  const allFAQ = [...filteredFAQ, ...generalFAQ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle className="text-2xl">Frequently Asked Questions</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground mb-4">
              Find answers to common questions about your program, platform access, and available resources.
            </p>
            
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                enhanced
                placeholder="Search FAQ..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* FAQ Items */}
      <div className="space-y-4">
        {allFAQ.length > 0 ? (
          allFAQ.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
            >
              <AuthCard className="group hover:border-primary/40 transition-colors duration-300">
                <AuthCardContent className="p-0">
                  <button
                    onClick={() => toggleExpanded(index)}
                    className="w-full p-6 text-left hover:bg-primary/5 transition-colors duration-200 rounded-lg"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-white pr-4 group-hover:text-primary transition-colors duration-300">
                        {item.question}
                      </h3>
                      <div className="flex-shrink-0">
                        {expandedItems.has(index) ? (
                          <ChevronUp className="h-5 w-5 text-primary" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors duration-300" />
                        )}
                      </div>
                    </div>
                  </button>
                  
                  <AnimatePresence>
                    {expandedItems.has(index) && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: 'easeInOut' }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-6">
                          <div className="border-t border-primary/20 pt-4">
                            <p className="text-muted-foreground leading-relaxed">
                              {item.answer}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </AuthCardContent>
              </AuthCard>
            </motion.div>
          ))
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <AuthCard>
              <AuthCardContent className="text-center py-8">
                <p className="text-muted-foreground">No FAQ items match your search.</p>
              </AuthCardContent>
            </AuthCard>
          </motion.div>
        )}
      </div>

      {/* Contact Support */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Still Need Help?</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground mb-6">
              Can't find the answer you're looking for? Our team is here to help you succeed.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <AuthButton variant="outline" className="justify-start h-auto py-4">
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-2 mb-1">
                    <MessageCircle className="h-4 w-4 text-primary" />
                    <span className="font-medium">Chat with Mentor</span>
                  </div>
                  <span className="text-xs text-muted-foreground">Get personalized guidance</span>
                </div>
              </AuthButton>
              
              <AuthButton variant="outline" className="justify-start h-auto py-4">
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-2 mb-1">
                    <Mail className="h-4 w-4 text-primary" />
                    <span className="font-medium">Email Support</span>
                  </div>
                  <span className="text-xs text-muted-foreground"><EMAIL></span>
                </div>
              </AuthButton>
              
              <AuthButton variant="outline" className="justify-start h-auto py-4">
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-2 mb-1">
                    <Phone className="h-4 w-4 text-primary" />
                    <span className="font-medium">Office Hours</span>
                  </div>
                  <span className="text-xs text-muted-foreground">Weekly Q&A sessions</span>
                </div>
              </AuthButton>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Pro Tips</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-white mb-2">💡 Getting Quick Answers</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Check the FAQ first before reaching out</li>
                  <li>• Use specific keywords when searching</li>
                  <li>• Join the Slack community for peer support</li>
                </ul>
              </div>
              <div>
                <h4 className="text-sm font-medium text-white mb-2">🚀 Maximizing Support</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Prepare specific questions for mentor meetings</li>
                  <li>• Attend weekly office hours</li>
                  <li>• Document your challenges and solutions</li>
                </ul>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
