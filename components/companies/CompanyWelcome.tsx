'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Company } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle, AuthCardDescription } from '@/components/ui/auth-card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Users, Target, ExternalLink } from 'lucide-react';

interface CompanyWelcomeProps {
  company: Company;
}

export function CompanyWelcome({ company }: CompanyWelcomeProps) {
  const joinedDate = new Date(company.joinDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="mb-8"
    >
      <AuthCard className="overflow-hidden">
        <div className="relative">
          {/* Background gradient */}
          <div className={`absolute inset-0 bg-gradient-to-r ${company.programTrack.color} opacity-10`} />
          
          <AuthCardHeader className="relative">
            <div className="flex flex-col lg:flex-row items-center gap-6">
              {/* Company Logo */}
              <motion.div
                className="relative"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="w-24 h-24 lg:w-32 lg:h-32 rounded-2xl bg-white/10 backdrop-blur-md border border-primary/20 flex items-center justify-center overflow-hidden">
                  {company.logo ? (
                    <Image
                      src={company.logo}
                      alt={`${company.name} logo`}
                      width={96}
                      height={96}
                      className="object-contain"
                    />
                  ) : (
                    <span className="text-4xl">{company.programTrack.icon}</span>
                  )}
                </div>
              </motion.div>

              {/* Company Info */}
              <div className="flex-1 text-center lg:text-left">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <AuthCardTitle className="text-3xl lg:text-4xl mb-2">
                    Welcome to {company.name}
                  </AuthCardTitle>
                  <AuthCardDescription className="text-lg mb-4">
                    {company.description}
                  </AuthCardDescription>
                  
                  {/* Badges */}
                  <div className="flex flex-wrap justify-center lg:justify-start gap-2 mb-4">
                    <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                      {company.industry}
                    </Badge>
                    <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                      {company.currentStage}
                    </Badge>
                    <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                      {company.programTrack.name}
                    </Badge>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center justify-center lg:justify-start gap-2">
                      <Calendar className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">Joined {joinedDate}</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start gap-2">
                      <Users className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">{company.mentors.length} Mentors</span>
                    </div>
                    <div className="flex items-center justify-center lg:justify-start gap-2">
                      <Target className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">{company.progress.overall}% Complete</span>
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Website Link */}
              {company.website && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <a
                    href={company.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 px-4 py-2 bg-primary/20 hover:bg-primary/30 border border-primary/30 rounded-lg text-primary transition-colors duration-200"
                  >
                    <ExternalLink className="h-4 w-4" />
                    Visit Website
                  </a>
                </motion.div>
              )}
            </div>
          </AuthCardHeader>
        </div>

        {/* Progress Bar */}
        <AuthCardContent className="pt-0">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="space-y-4"
          >
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-white">Overall Progress</span>
                <span className="text-sm text-muted-foreground">{company.progress.overall}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-primary to-purple-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${company.progress.overall}%` }}
                  transition={{ duration: 1, delay: 0.6, ease: "easeOut" }}
                />
              </div>
            </div>

            {/* Next Steps Preview */}
            <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
              <h4 className="text-sm font-medium text-white mb-2">Next Steps</h4>
              <ul className="space-y-1">
                {company.nextSteps.slice(0, 3).map((step, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                    className="text-sm text-muted-foreground flex items-center gap-2"
                  >
                    <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                    {step}
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        </AuthCardContent>
      </AuthCard>
    </motion.div>
  );
}
