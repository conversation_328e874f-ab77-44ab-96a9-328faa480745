'use client';

import { motion } from 'framer-motion';
import { Milestone } from '@/lib/data/companies';
import { AuthCard, AuthCardContent, AuthCardHeader, AuthCardTitle } from '@/components/ui/auth-card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, AlertTriangle, Circle, Calendar, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MilestoneTimelineProps {
  milestones: Milestone[];
}

export function MilestoneTimeline({ milestones }: MilestoneTimelineProps) {
  const getStatusIcon = (status: Milestone['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'in-progress':
        return <Clock className="h-5 w-5 text-blue-400" />;
      case 'overdue':
        return <AlertTriangle className="h-5 w-5 text-red-400" />;
      default:
        return <Circle className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: Milestone['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'in-progress':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'overdue':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-muted/20 text-muted-foreground border-muted/30';
    }
  };

  const getPriorityColor = (priority: Milestone['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default:
        return 'bg-green-500/20 text-green-400 border-green-500/30';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const sortedMilestones = [...milestones].sort((a, b) => 
    new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle className="text-2xl">Milestone Timeline</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <p className="text-muted-foreground mb-4">
              Track your progress through key milestones and deliverables. Stay on top of deadlines and celebrate your achievements.
            </p>
            
            {/* Progress Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {['completed', 'in-progress', 'pending', 'overdue'].map((status) => {
                const count = milestones.filter(m => m.status === status).length;
                return (
                  <div key={status} className="text-center">
                    <div className="text-2xl font-bold text-white">{count}</div>
                    <div className="text-sm text-muted-foreground capitalize">{status.replace('-', ' ')}</div>
                  </div>
                );
              })}
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>

      {/* Timeline */}
      <div className="space-y-4">
        {sortedMilestones.map((milestone, index) => (
          <motion.div
            key={milestone.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <AuthCard className={cn(
              "group hover:border-primary/40 transition-colors duration-300",
              milestone.status === 'completed' && "border-green-500/30",
              milestone.status === 'overdue' && "border-red-500/30"
            )}>
              <AuthCardContent className="p-6">
                <div className="flex items-start gap-4">
                  {/* Timeline Indicator */}
                  <div className="flex flex-col items-center">
                    <div className={cn(
                      "p-2 rounded-full border",
                      milestone.status === 'completed' && "bg-green-500/20 border-green-500/30",
                      milestone.status === 'in-progress' && "bg-blue-500/20 border-blue-500/30",
                      milestone.status === 'overdue' && "bg-red-500/20 border-red-500/30",
                      milestone.status === 'pending' && "bg-muted/20 border-muted/30"
                    )}>
                      {getStatusIcon(milestone.status)}
                    </div>
                    {index < sortedMilestones.length - 1 && (
                      <div className="w-px h-16 bg-gradient-to-b from-primary/30 to-transparent mt-2" />
                    )}
                  </div>

                  {/* Milestone Content */}
                  <div className="flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-3">
                      <h3 className="text-lg font-semibold text-white">{milestone.title}</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className={getStatusColor(milestone.status)}>
                          {milestone.status.replace('-', ' ')}
                        </Badge>
                        <Badge variant="secondary" className={getPriorityColor(milestone.priority)}>
                          {milestone.priority} priority
                        </Badge>
                      </div>
                    </div>

                    <p className="text-muted-foreground mb-3">{milestone.description}</p>

                    {/* Due Date */}
                    <div className="flex items-center gap-2 mb-3">
                      <Calendar className="h-4 w-4 text-primary" />
                      <span className="text-sm text-muted-foreground">
                        Due: {formatDate(milestone.dueDate)}
                      </span>
                      {new Date(milestone.dueDate) < new Date() && milestone.status !== 'completed' && (
                        <Badge variant="secondary" className="bg-red-500/20 text-red-400 border-red-500/30 text-xs">
                          Overdue
                        </Badge>
                      )}
                    </div>

                    {/* Resources */}
                    {milestone.resources && milestone.resources.length > 0 && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium text-white">Resources</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {milestone.resources.map((resource, resourceIndex) => (
                            <Badge
                              key={resourceIndex}
                              variant="secondary"
                              className="bg-primary/10 text-primary border-primary/20 text-xs cursor-pointer hover:bg-primary/20 transition-colors"
                            >
                              {resource}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </AuthCardContent>
            </AuthCard>
          </motion.div>
        ))}
      </div>

      {/* Tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <AuthCard>
          <AuthCardHeader>
            <AuthCardTitle>Milestone Tips</AuthCardTitle>
          </AuthCardHeader>
          <AuthCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-white mb-2">Staying on Track</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Break large milestones into smaller tasks</li>
                  <li>• Set weekly check-ins with your mentor</li>
                  <li>• Use the provided resources and templates</li>
                </ul>
              </div>
              <div>
                <h4 className="text-sm font-medium text-white mb-2">Need Help?</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Reach out to your assigned mentor</li>
                  <li>• Join the weekly office hours</li>
                  <li>• Connect with peer companies in Slack</li>
                </ul>
              </div>
            </div>
          </AuthCardContent>
        </AuthCard>
      </motion.div>
    </div>
  );
}
