'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoadingLineReveal } from './ui/loading-line-reveal';

export default function InitialLoadingOverlay() {
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [showOverlay, setShowOverlay] = useState(true);

  // Check if this is the first load
  useEffect(() => {
    try {
      // For development: Check for URL parameter to force show the overlay
      const urlParams = new URLSearchParams(window.location.search);
      const forceShow = urlParams.get('showLoading');

      if (forceShow === 'true') {
        // Reset the loading state for testing
        localStorage.removeItem('hasLoadedSite');
        setShowOverlay(true);
        setLoading(true);
        return;
      }

      const hasLoaded = localStorage.getItem('hasLoadedSite');
      if (hasLoaded === 'true') {
        setShowOverlay(false);
        setLoading(false);
        return;
      }
      localStorage.setItem('hasLoadedSite', 'true');
    } catch (error) {
      // If localStorage is not available, always show the overlay
      console.error('localStorage error:', error);
    }
  }, []);

  useEffect(() => {
    // If we're not showing the overlay, don't run the loading animation
    if (!showOverlay) return;
    // Start time to ensure minimum display time
    const startTime = Date.now();
    const minDisplayTime = 3000; // 3 seconds minimum display time
    const duration = 3.5; // seconds to complete the loading animation

    // Simulate loading progress
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + (100 / (duration * 10));
        return newProgress > 100 ? 100 : newProgress;
      });
    }, 100);

    // Handle window load event
    const handleLoad = () => {
      clearInterval(interval);
      setProgress(100);

      // Ensure minimum display time
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

      setTimeout(() => {
        setLoading(false);
      }, remainingTime);
    };

    // Listen for window load event
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
    }

    return () => {
      clearInterval(interval);
      window.removeEventListener('load', handleLoad);
    };
  }, [showOverlay]);

  return (
    <AnimatePresence>
      {showOverlay && loading && (
        <motion.div
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.8, ease: 'easeInOut' }}
          className="fixed inset-0 z-[9999] flex flex-col items-center justify-center bg-black"
        >
          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(147,51,234,0.15),transparent_70%)] pointer-events-none"></div>

            {/* Animated glow effects */}
            <motion.div
              className="absolute w-64 h-64 rounded-full bg-purple-500/10 blur-3xl"
              style={{ top: '30%', left: '20%' }}
              animate={{
                opacity: [0.3, 0.5, 0.3],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: 'reverse',
              }}
            />
            <motion.div
              className="absolute w-96 h-96 rounded-full bg-purple-700/10 blur-3xl"
              style={{ bottom: '20%', right: '10%' }}
              animate={{
                opacity: [0.2, 0.4, 0.2],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                repeatType: 'reverse',
              }}
            />
          </div>

          <div className="w-full max-w-md px-4 flex flex-col items-center relative z-10">
            {/* Logo or branding element */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-12"
            >
              <div className="relative w-20 h-20 bg-gradient-to-br from-purple-700 to-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-4xl">I</span>

                {/* Pulsing ring */}
                <motion.div
                  className="absolute -inset-2 rounded-full border border-purple-500/50"
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.5, 0.8, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: 'reverse',
                  }}
                />
              </div>
            </motion.div>

            {/* Progress container */}
            <div className="w-full mb-4">
              <LoadingLineReveal progress={progress} height={3} />
            </div>

            {/* Percentage indicator */}
            <div className="flex items-center justify-between w-full mt-2 mb-6">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-purple-400 text-sm font-light"
              >
                Loading
              </motion.div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-white text-sm font-medium"
              >
                {Math.round(progress)}%
              </motion.div>
            </div>

            {/* Subtle animated dots */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="mt-8 flex space-x-3 items-center justify-center"
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  animate={{
                    opacity: [0.3, 1, 0.3],
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                  className="w-2 h-2 rounded-full bg-purple-500"
                />
              ))}
            </motion.div>

            {/* Tagline */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.7 }}
              transition={{ delay: 1 }}
              className="mt-12 text-xs text-white/50 font-light tracking-wider"
            >
              INNOVATION • TECHNOLOGY • FUTURE
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
