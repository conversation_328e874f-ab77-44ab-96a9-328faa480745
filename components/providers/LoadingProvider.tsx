'use client';

import { ReactNode, useState, useEffect } from 'react';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

interface LoadingProviderProps {
  children: ReactNode;
}

export function LoadingProvider({ children }: Readonly<LoadingProviderProps>) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // This ensures the component is mounted on the client
    setMounted(true);
  }, []);

  return (
    <>
      {mounted && <LoadingOverlay />}
      {children}
    </>
  );
}
