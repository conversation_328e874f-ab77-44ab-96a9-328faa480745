'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useScroll, useSpring } from 'framer-motion';

interface SmoothScrollContextType {
  scrollYProgress: any;
}

const SmoothScrollContext = createContext<SmoothScrollContextType | null>(null);

export const useSmoothScroll = () => {
  const context = useContext(SmoothScrollContext);
  if (!context) {
    throw new Error('useSmoothScroll must be used within a SmoothScrollProvider');
  }
  return context;
};

interface SmoothScrollProviderProps {
  children: ReactNode;
}

export function SmoothScrollProvider({ children }: SmoothScrollProviderProps) {
  const [isMounted, setIsMounted] = useState(false);
  
  // Set up smooth scrolling
  const { scrollYProgress } = useScroll();
  const smoothScrollY = useSpring(scrollYProgress, {
    damping: 50,
    stiffness: 400,
    mass: 3
  });

  // Only enable on client-side
  useEffect(() => {
    setIsMounted(true);
    
    // Add smooth scroll behavior to html element
    document.documentElement.style.scrollBehavior = 'smooth';
    
    return () => {
      document.documentElement.style.scrollBehavior = '';
    };
  }, []);

  if (!isMounted) {
    return <>{children}</>;
  }

  return (
    <SmoothScrollContext.Provider value={{ scrollYProgress: smoothScrollY }}>
      {/* Main content */}
      <div className="smooth-scroll-container">
        {children}
      </div>
    </SmoothScrollContext.Provider>
  );
}

// Hook for creating scroll-triggered animations
export function useScrollAnimation(threshold = 0.1) {
  const [ref, setRef] = useState<HTMLElement | null>(null);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { threshold }
    );

    observer.observe(ref);
    return () => observer.disconnect();
  }, [ref, threshold]);

  return { ref: setRef, isInView };
}
