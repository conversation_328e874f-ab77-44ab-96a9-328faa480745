"use client";
import { cn } from "@/lib/utils";
import React, {
  useState,
  useEffect,
  useRef,
  // RefObject, // Removed as it's no longer explicitly used for canvasRef type
  useCallback,
} from "react";

interface StarProps {
  x: number;
  y: number;
  radius: number;
  opacity: number;
  twinkleSpeed: number | null;
  initialOpacity: number; // Store initial opacity for reset
}

interface StarBackgroundProps {
  starDensity?: number;
  allStarsTwinkle?: boolean;
  twinkleProbability?: number;
  minTwinkleSpeed?: number;
  maxTwinkleSpeed?: number;
  className?: string;
}

export const StarsBackground: React.FC<StarBackgroundProps> = ({
  starDensity = 0.00015, // Adjusted for better visual density
  allStarsTwinkle = true,
  twinkleProbability = 0.7,
  minTwinkleSpeed = 0.5, // seconds for a half-cycle
  maxTwinkleSpeed = 1,   // seconds for a half-cycle
  className,
}) => {
  const [stars, setStars] = useState<StarProps[]>([]);
  const canvasRef = useRef<HTMLCanvasElement | null>(null); // Corrected type
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  const generateStars = useCallback(
    (width: number, height: number): StarProps[] => {
      if (width === 0 || height === 0) return [];
      const area = width * height;
      const numStars = Math.floor(area * starDensity);
      return Array.from({ length: numStars }, () => {
        const shouldTwinkle =
          allStarsTwinkle || Math.random() < twinkleProbability;
        const initialOpacityValue = Math.random() * 0.5 + 0.5;
        return {
          x: Math.random() * width,
          y: Math.random() * height,
          radius: Math.random() * 0.8 + 0.2, // Slightly larger stars
          opacity: initialOpacityValue,
          initialOpacity: initialOpacityValue,
          twinkleSpeed: shouldTwinkle
            ? minTwinkleSpeed +
              Math.random() * (maxTwinkleSpeed - minTwinkleSpeed)
            : null,
        };
      });
    },
    [
      starDensity,
      allStarsTwinkle,
      twinkleProbability,
      minTwinkleSpeed,
      maxTwinkleSpeed,
    ]
  );

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const updateDimensionsAndStars = () => {
      const { width, height } = canvas.getBoundingClientRect();
      if (width > 0 && height > 0) {
        canvas.width = width;
        canvas.height = height;
        setDimensions({ width, height });
        setStars(generateStars(width, height));
      }
    };
    
    updateDimensionsAndStars(); // Initial setup

    const resizeObserver = new ResizeObserver(updateDimensionsAndStars);
    resizeObserver.observe(canvas);

    return () => {
      resizeObserver.unobserve(canvas);
    };
  }, [generateStars]);


  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || dimensions.width === 0 || dimensions.height === 0) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    let animationFrameId: number;

    const render = (timestamp: number) => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      stars.forEach((star) => {
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
        
        if (star.twinkleSpeed !== null) {
          // Sine wave for smooth twinkle: ranges from -1 to 1. We want opacity 0.1 to initialOpacity.
          const twinkleValue = Math.sin((timestamp * 0.001) / star.twinkleSpeed); // Slow down twinkle
          star.opacity = star.initialOpacity * 0.5 + (twinkleValue * star.initialOpacity * 0.4);
          star.opacity = Math.max(0.1, Math.min(star.opacity, star.initialOpacity)); // Clamp opacity
        }
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`;
        ctx.fill();
      });

      animationFrameId = requestAnimationFrame(render);
    };

    animationFrameId = requestAnimationFrame(render);

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [stars, dimensions]);

  return (
    <canvas
      ref={canvasRef}
      className={cn("h-full w-full absolute inset-0 pointer-events-none", className)} // Added pointer-events-none
    />
  );
};
