'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedGradientBackgroundProps {
  className?: string;
  children: React.ReactNode;
}

export function AnimatedGradientBackground({
  className = '',
  children,
}: AnimatedGradientBackgroundProps) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-background/80 backdrop-blur-3xl z-10" />
        <motion.div
          className="absolute -inset-[100px] opacity-50"
          initial={{ backgroundPosition: '0% 0%' }}
          animate={{ 
            backgroundPosition: ['0% 0%', '100% 100%', '0% 0%'],
          }}
          transition={{ 
            repeat: Infinity,
            duration: 20,
            ease: 'linear',
          }}
          style={{
            background: 'radial-gradient(circle at center, hsl(var(--primary)) 0%, transparent 70%)',
            filter: 'blur(50px)',
          }}
        />
        <motion.div
          className="absolute -inset-[100px] opacity-30"
          initial={{ backgroundPosition: '100% 100%' }}
          animate={{ 
            backgroundPosition: ['100% 100%', '0% 0%', '100% 100%'],
          }}
          transition={{ 
            repeat: Infinity,
            duration: 25,
            ease: 'linear',
          }}
          style={{
            background: 'radial-gradient(circle at center, hsl(var(--secondary)) 0%, transparent 70%)',
            filter: 'blur(50px)',
          }}
        />
      </div>
      <div className="relative z-10">{children}</div>
    </div>
  );
}
