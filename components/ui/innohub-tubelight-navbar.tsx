"use client"

import React, { useEffect, useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { LucideIcon, Home, User, Briefcase, TrendingUp, Users, Newspaper, Globe, Menu } from "lucide-react"
import { cn } from "@/lib/utils"
import { useLanguage } from "@/lib/context/language-context"
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { HoverBorderGradient } from '@/components/ui/hover-border-gradient'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
  key: string
}

interface InnoHubTubeLightNavBarProps {
  className?: string
}

const navItems: NavItem[] = [
  { name: 'Home', url: '/', icon: Home, key: 'nav.home' },
  { name: 'About', url: '/about', icon: User, key: 'nav.about' },
  { name: 'Programs', url: '/programs', icon: Briefcase, key: 'nav.programs' },
  { name: 'Success Stories', url: '/success-stories', icon: TrendingUp, key: 'nav.successStories' },
  { name: 'Investors', url: '/investors', icon: Users, key: 'nav.investors' },
  { name: 'News', url: '/news', icon: Newspaper, key: 'nav.news' },
]

export function InnoHubTubeLightNavBar({ className }: InnoHubTubeLightNavBarProps) {
  const [scrolled, setScrolled] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()
  const { t, language, setLanguage, isLoaded } = useLanguage()

  // Find active tab based on current pathname
  const activeItem = navItems.find(item => item.url === pathname) || navItems[0]

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    const handleScroll = () => {
      const isScrolled = window.scrollY > 10
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled)
      }
    }

    handleResize()
    handleScroll()
    window.addEventListener("resize", handleResize)
    window.addEventListener('scroll', handleScroll)
    
    return () => {
      window.removeEventListener("resize", handleResize)
      window.removeEventListener('scroll', handleScroll)
    }
  }, [scrolled])

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        scrolled ? 'bg-black/80 backdrop-blur-md border-b border-purple-500' : 'bg-transparent',
        className,
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        {/* Logo (Left) */}
        <Link href="/" className="flex items-center gap-2 z-10">
          <div className="relative w-10 h-10 rounded-full overflow-hidden">
            <Image
              src="/images/logo/innohub_logo.png"
              alt="InnoHub Logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
        </Link>

        {/* Desktop Tubelight Navigation (Center) */}
        <nav className="hidden md:flex items-center absolute left-1/2 transform -translate-x-1/2">
          <div className="flex items-center gap-1 bg-black/20 border border-purple-500/30 backdrop-blur-lg py-1 px-1 rounded-full shadow-lg">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.url

              return (
                <Link
                  key={item.url}
                  href={item.url}
                  className={cn(
                    "relative cursor-pointer text-sm font-semibold px-4 py-2 rounded-full transition-all duration-300",
                    "text-white/80 hover:text-white",
                    isActive && "text-white",
                  )}
                >
                  <span className="relative z-10">{isLoaded ? t(item.key) : item.name}</span>
                  {isActive && (
                    <motion.div
                      layoutId="tubelight"
                      className="absolute inset-0 w-full bg-purple-600/20 rounded-full -z-10"
                      initial={false}
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 30,
                      }}
                    >
                      {/* Tubelight effect */}
                      <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-purple-500 rounded-t-full">
                        <div className="absolute w-12 h-6 bg-purple-500/30 rounded-full blur-md -top-2 -left-2" />
                        <div className="absolute w-8 h-6 bg-purple-500/40 rounded-full blur-md -top-1" />
                        <div className="absolute w-4 h-4 bg-purple-500/50 rounded-full blur-sm top-0 left-2" />
                      </div>
                    </motion.div>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Right Side Actions */}
        <div className="hidden md:flex items-center gap-4 z-10">
          {/* Courses Button */}
          <Link href="/courses">
            <HoverBorderGradient
              containerClassName="rounded-full"
              className="dark:bg-slate-950 bg-slate-950 text-white px-4 py-1.5 text-sm"
              duration={2}
            >
              {isLoaded ? t('nav.courses') : 'Courses'}
            </HoverBorderGradient>
          </Link>

          {/* Language Switcher */}
          {isLoaded && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <HoverBorderGradient
                  containerClassName="rounded-full h-[2.25rem] w-[3rem]"
                  className="dark:bg-slate-950 bg-slate-950 text-white flex items-center justify-center gap-1 h-full w-full px-2"
                  duration={2}
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm">{language === 'en' ? '🇺🇸' : '🇲🇳'}</span>
                  <span className="sr-only">{t('nav.language')}</span>
                </HoverBorderGradient>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-black/90 backdrop-blur-md border border-purple-500 rounded-xl">
                <DropdownMenuItem
                  className={`${language === 'en' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('en')}
                >
                  <span>🇺🇸</span>
                  {t('nav.english')}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${language === 'mn' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('mn')}
                >
                  <span>🇲🇳</span>
                  {t('nav.mongolian')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Mobile Navigation */}
        <Sheet>
          <SheetTrigger asChild className="md:hidden">
            <HoverBorderGradient
              containerClassName="rounded-full h-[2.75rem] md:hidden"
              className="dark:bg-slate-950 bg-slate-950 text-white flex items-center justify-center h-full px-3 py-1.5"
              duration={2}
            >
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle menu</span>
            </HoverBorderGradient>
          </SheetTrigger>
          <SheetContent side="right" className="w-[300px] sm:w-[400px] bg-black/95 border-purple-500">
            <nav className="flex flex-col gap-4 mt-8">
              <HoverBorderGradient
                containerClassName="rounded-2xl"
                className="dark:bg-slate-950 bg-slate-950 text-white flex flex-col w-full py-2 px-2"
                duration={2}
              >
                <div className="flex flex-col w-full">
                  {navItems.map((item) => (
                    <Link
                      key={item.url}
                      href={item.url}
                      className={cn(
                        "text-white/80 hover:text-white transition-colors py-3 px-4 text-base font-medium rounded-xl",
                        pathname === item.url ? 'text-white border-l-2 border-purple-500' : ''
                      )}
                    >
                      {isLoaded ? t(item.key) : item.name}
                    </Link>
                  ))}
                  <Link
                    href="/courses"
                    className="text-white/80 hover:text-white transition-colors py-3 px-4 text-base font-medium rounded-xl"
                  >
                    {isLoaded ? t('nav.courses') : 'Courses'}
                  </Link>
                </div>
              </HoverBorderGradient>
              {isLoaded && (
                <div className="mt-4 flex flex-col gap-4">
                  <div className="flex flex-col gap-2">
                    <p className="text-white/60 text-sm">{t('nav.language')}:</p>
                    <div className="flex gap-2">
                      <HoverBorderGradient
                        containerClassName="rounded-full h-[2.25rem]"
                        className={`dark:bg-slate-950 ${language === 'en' ? 'bg-purple-800' : 'bg-slate-950'} text-white flex items-center justify-center gap-2 px-4 py-1.5 text-sm`}
                        onClick={() => setLanguage('en')}
                        duration={2}
                      >
                        <span className="text-sm">🇺🇸</span>
                        {t('nav.english')}
                      </HoverBorderGradient>
                      <HoverBorderGradient
                        containerClassName="rounded-full h-[2.25rem]"
                        className={`dark:bg-slate-950 ${language === 'mn' ? 'bg-purple-800' : 'bg-slate-950'} text-white flex items-center justify-center gap-2 px-4 py-1.5 text-sm`}
                        onClick={() => setLanguage('mn')}
                        duration={2}
                      >
                        <span className="text-sm">🇲🇳</span>
                        {t('nav.mongolian')}
                      </HoverBorderGradient>
                    </div>
                  </div>
                </div>
              )}
            </nav>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  )
}
