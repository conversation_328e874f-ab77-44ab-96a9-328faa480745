'use client';

import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SmoothRevealProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  once?: boolean;
  threshold?: number;
}

export function SmoothReveal({
  children,
  className,
  delay = 0,
  duration = 0.8,
  direction = 'up',
  distance = 30,
  once = true,
  threshold = 0.1,
}: SmoothRevealProps) {
  // Set initial and animate values based on direction
  const getInitialPosition = () => {
    switch (direction) {
      case 'up': return { y: distance, opacity: 0 };
      case 'down': return { y: -distance, opacity: 0 };
      case 'left': return { x: distance, opacity: 0 };
      case 'right': return { x: -distance, opacity: 0 };
      default: return { y: distance, opacity: 0 };
    }
  };

  // Custom easing curve for smooth animation
  const easing = [0.19, 1, 0.22, 1]; // Exponential ease-out

  return (
    <motion.div
      className={cn(className)}
      initial={getInitialPosition()}
      whileInView={{ x: 0, y: 0, opacity: 1 }}
      viewport={{ once, amount: threshold }}
      transition={{ 
        duration, 
        delay, 
        ease: easing,
      }}
    >
      {children}
    </motion.div>
  );
}

interface StaggeredRevealProps {
  children: ReactNode[];
  className?: string;
  itemClassName?: string;
  staggerDelay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  once?: boolean;
  threshold?: number;
}

export function StaggeredReveal({
  children,
  className,
  itemClassName,
  staggerDelay = 0.1,
  duration = 0.8,
  direction = 'up',
  distance = 30,
  once = true,
  threshold = 0.1,
}: StaggeredRevealProps) {
  // Set initial and animate values based on direction
  const getInitialPosition = () => {
    switch (direction) {
      case 'up': return { y: distance, opacity: 0 };
      case 'down': return { y: -distance, opacity: 0 };
      case 'left': return { x: distance, opacity: 0 };
      case 'right': return { x: -distance, opacity: 0 };
      default: return { y: distance, opacity: 0 };
    }
  };

  // Custom easing curve for smooth animation
  const easing = [0.19, 1, 0.22, 1]; // Exponential ease-out

  return (
    <div className={className}>
      {children.map((child, index) => (
        <motion.div
          key={index}
          className={itemClassName}
          initial={getInitialPosition()}
          whileInView={{ x: 0, y: 0, opacity: 1 }}
          viewport={{ once, amount: threshold }}
          transition={{ 
            duration, 
            delay: index * staggerDelay, 
            ease: easing,
          }}
        >
          {child}
        </motion.div>
      ))}
    </div>
  );
}
