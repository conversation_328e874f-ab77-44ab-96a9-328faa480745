import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  enhanced?: boolean;
  isLoading?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, enhanced = false, isLoading = false, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);

    if (enhanced) {
      return (
        <div className="relative group">
          {/* Animated gradient border */}
          <motion.div
            className="absolute -inset-0.5 bg-gradient-to-r from-primary/50 via-purple-500/50 to-primary/50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            animate={{
              backgroundPosition: isFocused ? ['0% 50%', '100% 50%', '0% 50%'] : '0% 50%',
            }}
            transition={{
              duration: 3,
              repeat: isFocused ? Infinity : 0,
              ease: "linear"
            }}
          />

          {/* Focus ring */}
          <motion.div
            className="absolute -inset-0.5 bg-gradient-to-r from-primary via-purple-500 to-primary rounded-lg opacity-0"
            animate={{
              opacity: isFocused ? 0.6 : 0,
              scale: isFocused ? 1 : 0.95,
            }}
            transition={{ duration: 0.2 }}
          />

          <input
            type={type}
            className={cn(
              "relative flex h-12 w-full rounded-lg border border-primary/20 bg-black/60 backdrop-blur-md px-4 py-3 text-sm text-white placeholder:text-muted-foreground transition-all duration-200",
              "focus:outline-none focus:border-primary/40 focus:bg-black/80",
              "hover:border-primary/30 hover:bg-black/70",
              "disabled:cursor-not-allowed disabled:opacity-50",
              isLoading && "opacity-70 cursor-wait",
              className
            )}
            ref={ref}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />
        </div>
      );
    }

    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
