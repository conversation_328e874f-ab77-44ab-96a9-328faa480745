'use client';
import React, { useEffect, useRef, useState } from 'react';

const colors: string[] = [
    "#c32d27",
    "#f5c63f",
    "#457ec4",
    "#356fdb",
];

interface GradientCursorProps {
  isActive: boolean;
}

export default function GradientCursor({ isActive }: GradientCursorProps) {
    const mouse = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
    const delayedMouse = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
    const rafId = useRef<number | null>(null);
    const circles = useRef<(HTMLDivElement | null)[]>([]);
    const [size, setSize] = useState(30);
    const [blur, setBlur] = useState(2);

    // Update size and blur based on isActive
    useEffect(() => {
        setSize(isActive ? 40 : 30);
        setBlur(isActive ? 20 : 2);
    }, [isActive]);

    const lerp = (x: number, y: number, a: number): number => x * (1 - a) + y * a;

    const manageMouseMove = (e: MouseEvent) => {
        const { clientX, clientY } = e;
        mouse.current = {
            x: clientX,
            y: clientY,
        };
    };

    const animate = () => {
        const { x, y } = delayedMouse.current;

        delayedMouse.current = {
            x: lerp(x, mouse.current.x, 0.075),
            y: lerp(y, mouse.current.y, 0.075),
        };

        moveCircles(delayedMouse.current.x, delayedMouse.current.y);

        rafId.current = window.requestAnimationFrame(animate);
    };

    const moveCircles = (x: number, y: number) => {
        if (circles.current.length < 1) return;
        circles.current.forEach((circle) => {
            if (circle) {
                // Use direct DOM manipulation instead of GSAP
                circle.style.transform = `translate(${x}px, ${y}px) translate(-50%, -50%)`;
            }
        });
    };

    useEffect(() => {
        animate();
        window.addEventListener("mousemove", manageMouseMove);
        return () => {
            window.removeEventListener("mousemove", manageMouseMove);
            if (rafId.current !== null) {
                window.cancelAnimationFrame(rafId.current);
            }
        };
    }, []);

    return (
        <>
            {[...Array(4)].map((_, i) => (
                <div
                    key={i}
                    ref={el => { circles.current[i] = el; }}
                    style={{
                        backgroundColor: colors[i],
                        width: size,
                        height: size,
                        filter: `blur(${blur}px)`,
                        transition: `width 0.3s ease-out, height 0.3s ease-out, filter 0.3s ease-out`,
                    }}
                    className='top-0 left-0 fixed rounded-full mix-blend-difference pointer-events-none z-[9999]'
                />
            ))}
        </>
    );
}
