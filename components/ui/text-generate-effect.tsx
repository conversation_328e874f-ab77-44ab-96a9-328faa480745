"use client";

import { useEffect, useRef } from "react";
import { motion, stagger, useAnimate, useScroll, useTransform } from "framer-motion";
import { cn } from "@/lib/utils";

export const TextGenerateEffect = ({
  words,
  className,
  filter = true,
  duration = 0.5,
}: {
  words: string;
  className?: string;
  filter?: boolean;
  duration?: number;
}) => {
  const [scope, animate] = useAnimate();
  let wordsArray = words.split(" ");

  useEffect(() => {
    animate(
      "span",
      {
        opacity: 1,
        filter: filter ? "blur(0px)" : "none",
      },
      {
        duration: duration || 1,
        delay: stagger(0.2),
        ease: "easeOut", // Add easing for smoother animation
      }
    );
  }, [scope.current, animate, duration, filter]);

  const renderWords = () => {
    return (
      <motion.div ref={scope}>
        {wordsArray.map((word, idx) => {
          return (
            <motion.span
              key={word + idx}
              className="dark:text-white text-white"
              style={{
                opacity: 0.5, // Increased initial opacity to 50%
                filter: filter ? "blur(5px)" : "none",
              }}
            >
              {word}{" "}
            </motion.span>
          );
        })}
      </motion.div>
    );
  };

  return (
    <div className={cn("font-light w-full", className)}>
      <div className="mt-4 w-full">
        <div className="text-white leading-relaxed tracking-wide max-w-full">
          {renderWords()}
        </div>
      </div>
    </div>
  );
};

// New component for text gradient opacity on scroll effect
export const TextGradientOpacityOnScroll = ({
  words,
  className,
}: {
  words: string;
  className?: string;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const charactersRef = useRef<(HTMLSpanElement | null)[]>([]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 0.9", "start 0.1"]
  });

  // Split text into words and then characters, preserving word structure
  const wordsArray = words.split(' ');
  const allCharacters: { char: string; isSpace: boolean; wordIndex: number; charIndex: number }[] = [];

  wordsArray.forEach((word, wordIndex) => {
    word.split('').forEach((char, charIndex) => {
      allCharacters.push({
        char,
        isSpace: false,
        wordIndex,
        charIndex
      });
    });

    // Add space after each word except the last one
    if (wordIndex < wordsArray.length - 1) {
      allCharacters.push({
        char: ' ',
        isSpace: true,
        wordIndex,
        charIndex: -1
      });
    }
  });

  // Create individual opacity transforms for each character with staggered effect
  const characterOpacities = allCharacters.map((_, index) => {
    const progress = index / allCharacters.length;
    const start = Math.max(0, progress - 0.1);
    const end = Math.min(1, progress + 0.1);

    return useTransform(
      scrollYProgress,
      [start, end],
      [0.15, 1]
    );
  });

  return (
    <div ref={containerRef} className={cn("font-light w-full", className)}>
      <div className="mt-4 w-full">
        <div className="text-white leading-relaxed tracking-wide max-w-full">
          {allCharacters.map((character, index) => (
            <motion.span
              key={`char-${index}-${character.wordIndex}-${character.charIndex}`}
              ref={(el) => (charactersRef.current[index] = el)}
              style={{
                opacity: characterOpacities[index],
              }}
              className={character.isSpace ? "inline" : "inline-block"}
            >
              {character.char}
            </motion.span>
          ))}
        </div>
      </div>
    </div>
  );
};
