'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface CursorImageEffectProps {
  children: React.ReactNode;
  className?: string;
  images: string[];
  imageSize?: number;
  transitionDuration?: number;
  imageOpacity?: number;
  blurAmount?: number;
}

interface ImageInstance {
  id: number;
  imageIndex: number;
  x: number;
  y: number;
  createdAt: number;
}

export const CursorImageEffect = ({
  children,
  className = '',
  images,
  imageSize = 400,
  transitionDuration = 0.2,
  imageOpacity = 0.7,
  blurAmount = 10,
}: CursorImageEffectProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [imageInstances, setImageInstances] = useState<ImageInstance[]>([]);
  const [nextId, setNextId] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [isClicked, setIsClicked] = useState(false);
  const lastMoveTimeRef = useRef<number>(0);
  const moveThreshold = 20; // Increased minimum pixels moved to trigger a new image
  const lastPositionRef = useRef({ x: 0, y: 0 });

  // Auto-create images when not hovering - less frequently
  useEffect(() => {
    if (!isHovering && !isClicked) {
      const interval = setInterval(() => {
        if (containerRef.current) {
          const width = containerRef.current.offsetWidth;
          const height = containerRef.current.offsetHeight;

          // Create a new random position
          const x = Math.random() * width;
          const y = Math.random() * height;

          addNewImageInstance(
            x,
            y,
            Math.floor(Math.random() * images.length)
          );
        }
      }, 1500); // Less frequent auto-generation (1.5 seconds instead of 0.8)

      return () => clearInterval(interval);
    }
  }, [isHovering, isClicked, images.length]);

  // Clean up old images after 0.5 seconds
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      setImageInstances(prev =>
        prev.filter(img => now - img.createdAt < 500) // 0.5 seconds lifetime
      );
    }, 100); // Check frequently

    return () => clearInterval(cleanupInterval);
  }, []);

  // Add a new image instance at the specified position
  const addNewImageInstance = (x: number, y: number, imageIndex: number) => {
    const newInstance: ImageInstance = {
      id: nextId,
      imageIndex,
      x,
      y,
      createdAt: Date.now()
    };

    setImageInstances(prev => [...prev, newInstance]);
    setNextId(prev => prev + 1);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;

    const { left, top } = containerRef.current.getBoundingClientRect();
    const x = e.clientX - left;
    const y = e.clientY - top;

    setPosition({ x, y });
    setIsHovering(true);

    // Check if we've moved enough to create a new image
    // Increased threshold for less frequent image generation
    const dx = x - lastPositionRef.current.x;
    const dy = y - lastPositionRef.current.y;
    const distanceMoved = Math.sqrt(dx * dx + dy * dy);

    const now = Date.now();
    // Require more movement (20px instead of 5px) and longer time between images (300ms instead of 100ms)
    if (distanceMoved > 20 || now - lastMoveTimeRef.current > 300) {
      // Create a new image at this position
      const randomImageIndex = Math.floor(Math.random() * images.length);
      addNewImageInstance(x, y, randomImageIndex);

      // Update last position and time
      lastPositionRef.current = { x, y };
      lastMoveTimeRef.current = now;
    }
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
    if (!isClicked) {
      // Clear all images when mouse leaves unless clicked
      setImageInstances([]);
    }
  };

  const handleClick = () => {
    setIsClicked(!isClicked);

    // When clicked, freeze current images or resume normal behavior
    if (isClicked) {
      // Unfreeze - clear all images
      setImageInstances([]);
    } else {
      // Freeze current images - they'll stay until clicked again
      // We don't need to do anything as the cleanup won't run when isClicked is true
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
    >
      <AnimatePresence>
        {imageInstances.map((instance) => (
          <motion.div
            key={instance.id}
            className="pointer-events-none absolute z-10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{
              opacity: imageOpacity, // Using the prop value (0.8)
              scale: 1,
            }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            style={{
              width: imageSize,
              height: imageSize,
              left: instance.x - imageSize / 2,
              top: instance.y - imageSize / 2,
            }}
          >
            <div className="relative w-full h-full">
              <Image
                src={images[instance.imageIndex]}
                alt={`Image ${instance.imageIndex + 1}`}
                fill
                className="object-cover" // Square images
                style={{
                  // No blur effect
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent" />
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Background glow effect - follows cursor */}
      <motion.div
        className="pointer-events-none absolute -inset-px z-5"
        animate={{
          opacity: isHovering ? 0.2 : 0,
        }}
        transition={{ duration: 0.1 }}
        style={{
          background: `radial-gradient(${imageSize}px circle at ${position.x}px ${position.y}px, hsl(var(--primary)/0.15), transparent 70%)`,
        }}
      />

      {/* Subtle background pattern */}
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)]" />
      </div>

      {children}
    </div>
  );
};
