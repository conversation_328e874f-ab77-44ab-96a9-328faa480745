'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface InfoCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  className?: string;
  delay?: number;
  hoverEffect?: boolean;
  glowColor?: string;
}

export const InfoCard = ({
  title,
  description,
  icon,
  className,
  delay = 0,
  hoverEffect = true,
  glowColor = 'rgba(var(--primary), 0.15)',
}: InfoCardProps) => {
  return (
    <motion.div
      className={cn(
        'relative overflow-hidden rounded-xl border border-white/10 bg-black/20 backdrop-blur-md p-6',
        hoverEffect && 'transition-all duration-300 hover:-translate-y-1 hover:shadow-lg',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true }}
    >
      {/* Background glow effect */}
      <div 
        className="absolute inset-0 opacity-30 blur-xl"
        style={{ 
          background: `radial-gradient(circle at 50% 50%, ${glowColor}, transparent 70%)`,
        }}
      />
      
      {/* Content */}
      <div className="relative z-10">
        {icon && (
          <motion.div 
            className="mb-4 text-primary"
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, delay: delay + 0.2 }}
            viewport={{ once: true }}
          >
            {icon}
          </motion.div>
        )}
        
        <motion.h3 
          className="text-xl font-semibold mb-2"
          initial={{ opacity: 0, x: -10 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: delay + 0.3 }}
          viewport={{ once: true }}
        >
          {title}
        </motion.h3>
        
        <motion.p 
          className="text-muted-foreground"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: delay + 0.4 }}
          viewport={{ once: true }}
        >
          {description}
        </motion.p>
      </div>
      
      {/* Border glow on hover */}
      {hoverEffect && (
        <motion.div 
          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
        >
          <div className="absolute inset-0 border border-primary/30 rounded-xl" />
        </motion.div>
      )}
    </motion.div>
  );
};
