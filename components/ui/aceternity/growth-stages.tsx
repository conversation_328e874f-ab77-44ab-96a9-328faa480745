'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Interface for click-generated stars
interface ClickStar {
  id: string;
  x: number;
  y: number;
  color: string;
  size: number;
  delay: number;
}

// Interface for click ripple effects
interface ClickRipple {
  id: string;
  x: number;
  y: number;
  color: string;
}

const stageData = [
  {
    id: 1,
    title: "IDEA",
    description: "Spark of innovation",
    icon: "💡",
    color: "#FFD700",
    size: 60,
    position: { x: 10, y: 50 }
  },
  {
    id: 2,
    title: "BUILD",
    description: "Create & develop",
    icon: "🔨",
    color: "#32CD32",
    size: 80,
    position: { x: 25, y: 30 }
  },
  {
    id: 3,
    title: "LAUNCH",
    description: "Go to market",
    icon: "🚀",
    color: "#00BFFF",
    size: 100,
    position: { x: 50, y: 20 }
  },
  {
    id: 4,
    title: "SCALE",
    description: "Expand & grow",
    icon: "📈",
    color: "#9932CC",
    size: 120,
    position: { x: 75, y: 35 }
  },
  {
    id: 5,
    title: "SUCCESS",
    description: "Market leader",
    icon: "👑",
    color: "#FF69B4",
    size: 140,
    position: { x: 90, y: 55 }
  }
];

export const GrowthStages = ({
  className,
  activeStage = 3,
}: {
  className?: string;
  activeStage?: number;
}) => {
  const [hoveredStage, setHoveredStage] = useState<number | null>(null);
  const [clickStars, setClickStars] = useState<ClickStar[]>([]);
  const [clickRipples, setClickRipples] = useState<ClickRipple[]>([]);

  // Function to generate smooth star burst on click
  const generateStarBurst = useCallback((stageColor: string, clickX: number, clickY: number) => {
    const starCount = 12; // Increased number of stars for more dramatic effect
    const newStars: ClickStar[] = [];

    for (let i = 0; i < starCount; i++) {
      const angle = (i / starCount) * 2 * Math.PI;
      const distance = 40 + Math.random() * 120; // Varied distance from click point
      const x = clickX + Math.cos(angle) * distance;
      const y = clickY + Math.sin(angle) * distance;

      newStars.push({
        id: `click-star-${Date.now()}-${i}-${Math.random().toString(36).substring(2, 9)}`,
        x,
        y,
        color: stageColor,
        size: Math.random() * 4 + 2, // Random size between 2-6px
        delay: i * 0.04, // Slightly faster staggered timing
      });
    }

    setClickStars(prev => [...prev, ...newStars]);

    // Remove stars after animation completes
    setTimeout(() => {
      setClickStars(prev => prev.filter(star =>
        !newStars.some(newStar => newStar.id === star.id)
      ));
    }, 2200); // Slightly longer duration to match animation
  }, []);

  // Function to generate ripple effect on click
  const generateRipple = useCallback((stageColor: string, clickX: number, clickY: number) => {
    const rippleId = `ripple-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const newRipple: ClickRipple = {
      id: rippleId,
      x: clickX,
      y: clickY,
      color: stageColor,
    };

    setClickRipples(prev => [...prev, newRipple]);

    // Remove ripple after animation completes
    setTimeout(() => {
      setClickRipples(prev => prev.filter(ripple => ripple.id !== rippleId));
    }, 800);
  }, []);

  // Handle stage bubble click
  const handleStageClick = useCallback((stage: typeof stageData[0], event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // Get click position relative to the viewport for fixed positioning
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = rect.left + rect.width / 2; // Center of the bubble
    const clickY = rect.top + rect.height / 2; // Center of the bubble

    // Generate immediate ripple effect
    generateRipple(stage.color, clickX, clickY);

    // Generate star burst with a subtle delay for intentional feel
    setTimeout(() => {
      generateStarBurst(stage.color, clickX, clickY);
    }, 120); // Slightly longer delay for more intentional feel
  }, [generateStarBurst, generateRipple]);

  return (
    <div className={cn('relative py-16 lg:py-24 overflow-hidden', className)}>
      {/* Enhanced Cosmic Background */}
      <div className="absolute inset-0">
        {/* Optimized Animated Stars */}
        {Array.from({ length: 30 }).map((_, i) => {
          const starId = `star-${Math.random().toString(36).substring(2, 9)}-${i}`;
          return (
            <motion.div
              key={starId}
              className="absolute w-0.5 h-0.5 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                opacity: [0, 0.8, 0],
                scale: [0, 1.2, 0],
              }}
              transition={{
                duration: Math.random() * 4 + 3,
                repeat: Infinity,
                delay: Math.random() * 3,
                ease: [0.16, 1, 0.3, 1]
              }}
            />
          );
        })}

        {/* Enhanced Nebula Effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-radial from-purple-900/15 via-transparent to-transparent"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: [0.4, 0, 0.6, 1]
          }}
        />

        {/* Additional Cosmic Elements */}
        <motion.div
          className="absolute inset-0 bg-gradient-radial from-indigo-900/10 via-transparent to-transparent"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.1, 0.3, 0.1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: [0.4, 0, 0.6, 1]
          }}
        />
      </div>

      {/* Enhanced Main Growth Visualization */}
      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 h-[500px] lg:h-[650px] xl:h-[700px]">
        {/* Enhanced Growth Path - Smoother Curved Line */}
        <svg
          className="absolute inset-0 w-full h-full"
          viewBox="0 0 900 450"
          preserveAspectRatio="xMidYMid meet"
        >
          <defs>
            <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#FFD700" stopOpacity="0.4" />
              <stop offset="25%" stopColor="#32CD32" stopOpacity="0.6" />
              <stop offset="50%" stopColor="#00BFFF" stopOpacity="0.8" />
              <stop offset="75%" stopColor="#9932CC" stopOpacity="0.6" />
              <stop offset="100%" stopColor="#FF69B4" stopOpacity="0.4" />
            </linearGradient>

            <filter id="glow">
              <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>

            <filter id="softGlow">
              <feGaussianBlur stdDeviation="2" result="softBlur"/>
              <feMerge>
                <feMergeNode in="softBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>

          {/* Enhanced Animated Growth Path */}
          <motion.path
            d="M 100 380 Q 220 320, 350 280 Q 520 200, 680 170 Q 780 150, 850 120"
            stroke="url(#pathGradient)"
            strokeWidth="5"
            fill="none"
            filter="url(#glow)"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={{ pathLength: 1, opacity: 1 }}
            transition={{
              duration: 4,
              ease: [0.16, 1, 0.3, 1],
              opacity: { duration: 1, delay: 0.5 }
            }}
          />

          {/* Enhanced Progress Dots */}
          {stageData.map((stage, index) => {
            const pathProgress = (index + 1) / stageData.length;
            const x = 100 + (750 * pathProgress);
            const y = 380 - (260 * Math.sin(pathProgress * Math.PI * 0.75));

            return (
              <motion.circle
                key={stage.id}
                cx={x}
                cy={y}
                r="10"
                fill={stage.color}
                filter="url(#softGlow)"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  delay: index * 0.4 + 1.5,
                  duration: 0.6,
                  ease: [0.16, 1, 0.3, 1]
                }}
                className="drop-shadow-lg"
              />
            );
          })}
        </svg>

        {/* Enhanced Floating Stage Bubbles */}
        {stageData.map((stage, index) => {
          const isHovered = hoveredStage === stage.id;
          const isActive = index < activeStage;

          return (
            <motion.div
              key={stage.id}
              className="absolute cursor-pointer group"
              style={{
                left: `${stage.position.x}%`,
                top: `${stage.position.y}%`,
                transform: 'translate(-50%, -50%)',
              }}
              initial={{ scale: 0, opacity: 0 }}
              animate={{
                scale: 1,
                opacity: 1,
                y: [0, -12, 0],
              }}
              transition={{
                delay: index * 0.25,
                duration: 1,
                ease: [0.16, 1, 0.3, 1],
                y: {
                  duration: 4 + index * 0.5,
                  repeat: Infinity,
                  ease: [0.4, 0, 0.6, 1]
                }
              }}
              whileHover={{
                scale: 1.15,
                zIndex: 10,
                transition: { duration: 0.3, ease: [0.16, 1, 0.3, 1] }
              }}
              whileTap={{
                scale: 1.05,
                transition: { duration: 0.1, ease: [0.16, 1, 0.3, 1] }
              }}
              onHoverStart={() => setHoveredStage(stage.id)}
              onHoverEnd={() => setHoveredStage(null)}
              onClick={(event) => handleStageClick(stage, event)}
            >
              {/* Enhanced Bubble Container */}
              <motion.div
                className="relative"
                style={{ width: stage.size + 20, height: stage.size + 20 }}
              >
                {/* Enhanced Glow Effect */}
                <motion.div
                  className="absolute inset-0 rounded-full blur-xl"
                  style={{ backgroundColor: stage.color }}
                  animate={{
                    opacity: isHovered ? 0.6 : (isActive ? 0.3 : 0.1),
                    scale: isHovered ? 1.4 : 1,
                  }}
                  transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
                />

                {/* Additional Soft Glow */}
                <motion.div
                  className="absolute inset-2 rounded-full blur-lg"
                  style={{ backgroundColor: stage.color }}
                  animate={{
                    opacity: isHovered ? 0.4 : (isActive ? 0.2 : 0.05),
                    scale: isHovered ? 1.2 : 1,
                  }}
                  transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
                />

                {/* Enhanced Main Bubble */}
                <motion.div
                  className="relative w-full h-full rounded-full backdrop-blur-lg border-2 flex flex-col items-center justify-center overflow-hidden"
                  style={{
                    backgroundColor: `${stage.color}25`,
                    borderColor: stage.color,
                    boxShadow: `0 0 40px ${stage.color}50, inset 0 0 20px ${stage.color}20`
                  }}
                  animate={{
                    borderColor: isHovered ? stage.color : `${stage.color}70`,
                    backgroundColor: isHovered ? `${stage.color}35` : `${stage.color}25`,
                    boxShadow: isHovered
                      ? `0 0 60px ${stage.color}70, inset 0 0 30px ${stage.color}30`
                      : `0 0 40px ${stage.color}50, inset 0 0 20px ${stage.color}20`
                  }}
                  transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
                >
                  {/* Enhanced Floating Particles Inside Bubble */}
                  {Array.from({ length: 4 }).map((_, i) => {
                    const particleId = `particle-${stage.id}-${i}`;
                    return (
                      <motion.div
                        key={particleId}
                        className="absolute w-1 h-1 rounded-full"
                        style={{ backgroundColor: stage.color }}
                        animate={{
                          x: [0, 25, -25, 0],
                          y: [0, -20, 20, 0],
                          opacity: [0.2, 0.8, 0.2],
                          scale: [0.5, 1, 0.5],
                        }}
                        transition={{
                          duration: 5 + i * 0.5,
                          repeat: Infinity,
                          delay: i * 0.7,
                          ease: [0.4, 0, 0.6, 1]
                        }}
                      />
                    );
                  })}

                  {/* Enhanced Icon */}
                  <motion.div
                    className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl mb-2"
                    animate={{
                      rotate: isHovered ? [0, 360] : 0,
                      scale: isHovered ? 1.15 : 1,
                    }}
                    transition={{
                      duration: 0.6,
                      ease: [0.16, 1, 0.3, 1],
                      rotate: { duration: 0.8 }
                    }}
                  >
                    {stage.icon}
                  </motion.div>

                  {/* Enhanced Title */}
                  <motion.div
                    className="text-xs md:text-sm lg:text-base font-bold text-white text-center px-2 leading-tight"
                    animate={{
                      scale: isHovered ? 1.05 : 1,
                    }}
                    transition={{ duration: 0.3, ease: [0.16, 1, 0.3, 1] }}
                  >
                    {stage.title}
                  </motion.div>

                  {/* Enhanced Stage Number */}
                  <motion.div
                    className="absolute -top-3 -right-3 w-7 h-7 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg"
                    style={{
                      backgroundColor: stage.color,
                      boxShadow: `0 0 15px ${stage.color}60`
                    }}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{
                      delay: index * 0.25 + 0.8,
                      duration: 0.5,
                      ease: [0.16, 1, 0.3, 1]
                    }}
                    whileHover={{ scale: 1.1 }}
                  >
                    {stage.id}
                  </motion.div>
                </motion.div>
              </motion.div>

              {/* Enhanced Hover Description */}
              <AnimatePresence>
                {isHovered && (
                  <motion.div
                    className="absolute top-full mt-6 left-1/2 transform -translate-x-1/2 bg-black/90 backdrop-blur-lg rounded-xl p-4 border border-white/30 min-w-[220px] max-w-[280px] z-20 shadow-2xl"
                    initial={{ opacity: 0, y: -15, scale: 0.85 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -15, scale: 0.85 }}
                    transition={{
                      duration: 0.3,
                      ease: [0.16, 1, 0.3, 1]
                    }}
                  >
                    <div className="text-center">
                      <h4 className="text-white font-bold mb-2 text-base">{stage.title}</h4>
                      <p className="text-gray-300 text-sm leading-relaxed">{stage.description}</p>
                    </div>

                    {/* Enhanced Arrow */}
                    <div
                      className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-black/90"
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}

        {/* Enhanced Connecting Energy Beams */}
        {stageData.slice(0, -1).map((stage, index) => {
          const nextStage = stageData[index + 1];
          const isActive = index < activeStage - 1;

          return (
            <motion.div
              key={`beam-${stage.id}`}
              className="absolute pointer-events-none"
              style={{
                left: `${stage.position.x}%`,
                top: `${stage.position.y}%`,
                width: `${Math.abs(nextStage.position.x - stage.position.x)}%`,
                height: `${Math.abs(nextStage.position.y - stage.position.y)}%`,
                transform: 'translate(-50%, -50%)',
              }}
            >
              <motion.div
                className="w-full h-0.5 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 opacity-40 shadow-lg"
                initial={{ scaleX: 0, opacity: 0 }}
                animate={{
                  scaleX: isActive ? 1 : 0,
                  opacity: isActive ? 0.6 : 0
                }}
                transition={{
                  delay: index * 0.4 + 2.5,
                  duration: 1.2,
                  ease: [0.16, 1, 0.3, 1]
                }}
                style={{
                  transformOrigin: 'left',
                  transform: `rotate(${Math.atan2(
                    nextStage.position.y - stage.position.y,
                    nextStage.position.x - stage.position.x
                  ) * 180 / Math.PI}deg)`,
                  filter: 'blur(0.5px)'
                }}
              />
            </motion.div>
          );
        })}
      </div>

      {/* Enhanced Bottom Progress Indicator */}
      <motion.div
        className="mt-16 lg:mt-20 max-w-5xl mx-auto px-6"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: 3,
          duration: 1,
          ease: [0.16, 1, 0.3, 1]
        }}
      >
        <div className="flex justify-center items-center space-x-6 lg:space-x-8">
          {stageData.map((stage, index) => {
            const isActive = index < activeStage;

            return (
              <motion.div
                key={`progress-${stage.id}`}
                className="flex flex-col items-center cursor-pointer group"
                whileHover={{
                  scale: 1.05,
                  transition: { duration: 0.2 }
                }}
                onClick={() => setHoveredStage(stage.id)}
              >
                <motion.div
                  className="w-4 h-4 lg:w-5 lg:h-5 rounded-full mb-3 shadow-lg"
                  style={{
                    backgroundColor: isActive ? stage.color : '#374151',
                    boxShadow: isActive ? `0 0 15px ${stage.color}60` : 'none'
                  }}
                  animate={{
                    scale: isActive ? [1, 1.15, 1] : 1,
                    boxShadow: isActive
                      ? [`0 0 10px ${stage.color}40`, `0 0 20px ${stage.color}80`, `0 0 10px ${stage.color}40`]
                      : 'none'
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: isActive ? Infinity : 0,
                    ease: [0.4, 0, 0.6, 1]
                  }}
                />
                <span className="text-xs lg:text-sm text-gray-400 text-center font-medium group-hover:text-gray-300 transition-colors duration-200">
                  {stage.title}
                </span>
              </motion.div>
            );
          })}
        </div>
      </motion.div>

      {/* Click-Generated Ripple Effects */}
      <div className="fixed inset-0 pointer-events-none z-40">
        <AnimatePresence>
          {clickRipples.map((ripple) => (
            <motion.div
              key={ripple.id}
              className="absolute rounded-full border-2"
              style={{
                left: ripple.x - 25,
                top: ripple.y - 25,
                width: 50,
                height: 50,
                borderColor: ripple.color,
                backgroundColor: 'transparent',
              }}
              initial={{
                opacity: 0.8,
                scale: 0,
              }}
              animate={{
                opacity: 0,
                scale: 3,
              }}
              exit={{
                opacity: 0,
                scale: 0,
              }}
              transition={{
                duration: 0.6,
                ease: [0.25, 0.46, 0.45, 0.94],
              }}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Click-Generated Star Burst Effects */}
      <div className="fixed inset-0 pointer-events-none z-50">
        <AnimatePresence>
          {clickStars.map((star) => (
            <motion.div
              key={star.id}
              className="absolute rounded-full"
              style={{
                left: star.x - star.size / 2,
                top: star.y - star.size / 2,
                width: star.size,
                height: star.size,
                backgroundColor: star.color,
                boxShadow: `0 0 ${star.size * 3}px ${star.color}60, 0 0 ${star.size * 6}px ${star.color}30`,
                filter: 'blur(0.5px)',
              }}
              initial={{
                opacity: 0,
                scale: 0,
                rotate: 0,
                x: 0,
                y: 0,
              }}
              animate={{
                opacity: [0, 0.8, 1, 0.6, 0],
                scale: [0, 0.3, 0.8, 1.1, 1.3],
                rotate: [0, 90, 180, 270, 360],
                x: [0, Math.random() * 20 - 10, Math.random() * 30 - 15],
                y: [0, -15, -35, -55, -80],
              }}
              exit={{
                opacity: 0,
                scale: 0,
                transition: {
                  duration: 0.3,
                  ease: [0.4, 0, 1, 1]
                }
              }}
              transition={{
                duration: 1.8,
                delay: star.delay,
                ease: [0.25, 0.46, 0.45, 0.94], // Custom cubic-bezier for natural motion
                opacity: {
                  duration: 1.8,
                  times: [0, 0.15, 0.4, 0.8, 1],
                  ease: [0.25, 0.46, 0.45, 0.94]
                },
                scale: {
                  duration: 1.8,
                  times: [0, 0.2, 0.5, 0.8, 1],
                  ease: [0.25, 0.46, 0.45, 0.94]
                },
                rotate: {
                  duration: 1.8,
                  ease: [0.4, 0, 0.6, 1]
                },
                x: {
                  duration: 1.8,
                  times: [0, 0.3, 1],
                  ease: [0.25, 0.46, 0.45, 0.94]
                },
                y: {
                  duration: 1.8,
                  times: [0, 0.2, 0.5, 0.8, 1],
                  ease: [0.25, 0.46, 0.45, 0.94]
                }
              }}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};
