"use client";
import { cn } from "@/lib/utils";
import React, { useEffect, useState, useRef } from "react";

interface ShootingStar {
  id: number;
  x: number;
  y: number;
  angle: number;
  scale: number;
  speed: number;
  distance: number;
}

interface ShootingStarsProps {
  minSpeed?: number;
  maxSpeed?: number;
  minDelay?: number;
  maxDelay?: number;
  starColor?: string;
  trailColor?: string;
  starWidth?: number;
  starHeight?: number;
  className?: string;
}

const getRandomStartPoint = () => {
  const side = Math.floor(Math.random() * 4);
  // Ensure window is defined (client-side only)
  const innerWidth = typeof window !== 'undefined' ? window.innerWidth : 1920;
  const innerHeight = typeof window !== 'undefined' ? window.innerHeight : 1080;
  const offset = Math.random() * innerWidth; // Use innerWidth for offset consistency

  switch (side) {
    case 0: // Top edge
      return { x: offset, y: 0, angle: Math.random() * 120 + 30 }; // Angle towards bottom-ish
    case 1: // Right edge
      return { x: innerWidth, y: Math.random() * innerHeight, angle: Math.random() * 120 + 120 }; // Angle towards left-ish
    case 2: // Bottom edge
      return { x: offset, y: innerHeight, angle: Math.random() * 120 + 210 }; // Angle towards top-ish
    case 3: // Left edge
      return { x: 0, y: Math.random() * innerHeight, angle: Math.random() * 120 + 300 % 360 }; // Angle towards right-ish
    default:
      return { x: 0, y: 0, angle: 45 };
  }
};
export const ShootingStars: React.FC<ShootingStarsProps> = ({
  minSpeed = 10,
  maxSpeed = 30,
  minDelay = 1200,
  maxDelay = 4200,
  starColor = "#9E00FF", // Purple
  trailColor = "#2EB9DF", // Blueish
  starWidth = 10,
  starHeight = 1,
  className,
}) => {
  const [star, setStar] = useState<ShootingStar | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const updateDimensions = () => {
      setDimensions({
        width: typeof window !== 'undefined' ? window.innerWidth : 1920,
        height: typeof window !== 'undefined' ? window.innerHeight : 1080,
      });
    };
    updateDimensions();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updateDimensions);
      return () => window.removeEventListener('resize', updateDimensions);
    }
  }, []);


  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) return;

    const createStar = () => {
      const { x, y, angle } = getRandomStartPoint();
      const newStar: ShootingStar = {
        id: Date.now(),
        x,
        y,
        angle,
        scale: 1,
        speed: Math.random() * (maxSpeed - minSpeed) + minSpeed,
        distance: 0,
      };
      setStar(newStar);

      const randomDelay = Math.random() * (maxDelay - minDelay) + minDelay;
      const timerId = setTimeout(createStar, randomDelay);
      return () => clearTimeout(timerId);
    };

    const clearTimer = createStar();
    return clearTimer;
  }, [minSpeed, maxSpeed, minDelay, maxDelay, dimensions]);

  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) return () => {};

    const moveStar = () => {
      if (star) {
        setStar((prevStar) => {
          if (!prevStar) return null;
          const newX =
            prevStar.x +
            prevStar.speed * Math.cos((prevStar.angle * Math.PI) / 180);
          const newY =
            prevStar.y +
            prevStar.speed * Math.sin((prevStar.angle * Math.PI) / 180);
          const newDistance = prevStar.distance + prevStar.speed;
          const newScale = 1 + newDistance / 200; // Adjusted scale factor
          
          // Check bounds against current window dimensions
          if (
            newX < -starWidth * newScale || // Adjusted bounds check
            newX > dimensions.width + starWidth * newScale ||
            newY < -starHeight * 2 || // Adjusted bounds check
            newY > dimensions.height + starHeight * 2
          ) {
            return null; // Star is out of bounds
          }
          return {
            ...prevStar,
            x: newX,
            y: newY,
            distance: newDistance,
            scale: newScale,
          };
        });
      }
    };

    const animationFrame = requestAnimationFrame(moveStar);
    return () => cancelAnimationFrame(animationFrame);
  }, [star, dimensions, starWidth, starHeight]);

  return (
    <svg
      ref={svgRef}
      className={cn("w-full h-full absolute inset-0 pointer-events-none", className)} // Added pointer-events-none
    >
      {star && (
        <rect
          key={star.id}
          x={star.x}
          y={star.y}
          width={starWidth * star.scale}
          height={starHeight}
          fill="url(#shooting-star-gradient)"
          transform={`rotate(${star.angle}, ${
            star.x + (starWidth * star.scale) / 2
          }, ${star.y + starHeight / 2})`}
        />
      )}
      <defs>
        <linearGradient id="shooting-star-gradient" x1="0%" y1="0%" x2="100%" y2="0%"> {/* Adjusted gradient direction for trail */}
          <stop offset="0%" style={{ stopColor: trailColor, stopOpacity: 0 }} />
          <stop offset="70%" style={{ stopColor: trailColor, stopOpacity: 0.5 }} />
          <stop
            offset="100%"
            style={{ stopColor: starColor, stopOpacity: 1 }}
          />
        </linearGradient>
      </defs>
    </svg>
  );
};
