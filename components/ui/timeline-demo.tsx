"use client";

import React from "react";
import { motion } from "framer-motion";
import CircularGallery from "./CircularGallery";

export default function TimelineDemo() {
  // Enhanced items for the business incubator theme
  const galleryItems = [
    {
      image: "/images/Timeline/1.png",
      text: "Business Planning",
    },
    {
      image: "/images/Timeline/2.png",
      text: "Data Analytics",
    },
    {
      image: "/images/Timeline/3.png",
      text: "Workflow Automation",
    },
    {
      image: "/images/Background/background-of-4.png",
      text: "Mentorship Network",
    },
    {
      image: "/images/Background/background-of-4.png",
      text: "Funding Access",
    },
    {
      image: "/images/Background/background-of-4.png",
      text: "Workspace Solutions",
    },
    {
      image: "/images/Background/background-of-4.png",
      text: "Market Research",
    },
    {
      image: "/images/Background/background-of-4.png",
      text: "Legal Support",
    },
  ];

  return (
    <section className="py-32 bg-black relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-950/5 to-black"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(99,102,241,0.08),transparent_70%)]"></div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute w-64 h-64 rounded-full bg-purple-500/5 blur-3xl"
          style={{ top: '20%', left: '20%' }}
          animate={{
            opacity: [0.3, 0.6, 0.3],
            scale: [1, 1.3, 1],
            x: [0, 50, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute w-48 h-48 rounded-full bg-indigo-500/5 blur-3xl"
          style={{ bottom: '30%', right: '25%' }}
          animate={{
            opacity: [0.2, 0.5, 0.2],
            scale: [1, 1.2, 1],
            x: [0, -30, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        {/* Enhanced Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-block mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 100 }}
            transition={{ duration: 1, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="h-0.5 bg-gradient-to-r from-transparent via-purple-500 to-transparent"></div>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-purple-100 to-white bg-clip-text text-transparent leading-tight">
            Our Solutions
          </h2>

          <motion.p
            className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Explore our comprehensive suite of services designed to accelerate your startup journey from concept to market success.
          </motion.p>

          <motion.div
            className="mt-6 h-0.5 w-20 bg-gradient-to-r from-transparent via-purple-500 to-transparent mx-auto"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            transition={{ duration: 1, delay: 0.6 }}
            viewport={{ once: true }}
          />
        </motion.div>

        {/* Enhanced Gallery Container */}
        <motion.div
          className="relative"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.3, ease: [0.22, 1, 0.36, 1] }}
          viewport={{ once: true }}
        >
          {/* Gallery Background Glow */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-transparent to-indigo-500/10 rounded-3xl blur-xl"></div>

          {/* Gallery Container */}
          <div className="relative bg-black/20 backdrop-blur-sm rounded-3xl border border-purple-500/20 p-8 overflow-hidden">
            {/* Interactive Instructions */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              viewport={{ once: true }}
            >
              <p className="text-purple-300 text-sm font-medium">
                ← Drag or scroll to explore our solutions →
              </p>
            </motion.div>

            {/* CircularGallery with enhanced styling */}
            <div style={{ height: '600px', position: 'relative' }} className="rounded-2xl overflow-hidden">
              <CircularGallery
                items={galleryItems}
                bend={3}
                textColor="#ffffff"
                borderRadius={0.08}
                font="bold 32px 'Inter', sans-serif"
              />
            </div>

            {/* Bottom Gradient Overlay */}
            <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/60 to-transparent pointer-events-none"></div>
          </div>
        </motion.div>

        {/* Enhanced Call-to-Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <motion.button
              className="relative overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white border-0 px-8 py-4 text-lg font-semibold shadow-2xl shadow-purple-500/25 group"
              whileHover={{ boxShadow: "0 25px 50px rgba(147, 51, 234, 0.4)" }}
            >
              <span className="relative z-10">Learn More About Our Solutions</span>
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                initial={{ x: "-100%" }}
                whileHover={{ x: "100%" }}
                transition={{ duration: 0.6 }}
              />
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
