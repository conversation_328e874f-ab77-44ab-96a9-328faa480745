'use client';

import { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { AnimatedText } from '@/components/ui/animated-text';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ParallaxHeroProps {
  className?: string;
}

export function ParallaxHero({ className }: ParallaxHeroProps) {
  const ref = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"]
  });
  
  // Parallax effect for different elements
  const y1 = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, 400]);
  const y3 = useTransform(scrollYProgress, [0, 1], [0, 600]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);
  
  // Animated shapes
  const shapes = [
    { 
      className: "absolute top-20 left-[10%] w-64 h-64 rounded-full bg-primary/10 blur-3xl",
      y: useTransform(scrollYProgress, [0, 1], [0, -100]),
      scale: useTransform(scrollYProgress, [0, 1], [1, 0.8]),
    },
    { 
      className: "absolute top-40 right-[15%] w-96 h-96 rounded-full bg-secondary/10 blur-3xl",
      y: useTransform(scrollYProgress, [0, 1], [0, -150]),
      scale: useTransform(scrollYProgress, [0, 1], [1, 0.7]),
    },
    { 
      className: "absolute bottom-20 left-[20%] w-80 h-80 rounded-full bg-primary/5 blur-3xl",
      y: useTransform(scrollYProgress, [0, 1], [0, -200]),
      scale: useTransform(scrollYProgress, [0, 1], [1, 0.9]),
    },
  ];

  return (
    <div 
      ref={ref}
      className={cn(
        "relative min-h-[90vh] flex flex-col items-center justify-center overflow-hidden",
        "bg-gradient-to-b from-black via-black to-black/90",
        className
      )}
    >
      {/* Animated background shapes */}
      {shapes.map((shape, index) => (
        <motion.div
          key={index}
          className={shape.className}
          style={{ y: shape.y, scale: shape.scale }}
        />
      ))}
      
      {/* Grid overlay */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[length:50px_50px]" />
      
      {/* Content with parallax effect */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div style={{ y: y1, opacity }}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/20 px-4 py-1.5 text-sm font-medium text-primary mb-6 backdrop-blur-sm border border-primary/10"
            >
              Investor Network
            </motion.div>
          </motion.div>
          
          <motion.div style={{ y: y2, opacity }}>
            <AnimatedText
              text="Powering the Future of Innovation"
              className="text-4xl md:text-6xl font-bold tracking-tight mb-6 text-white"
            />
          </motion.div>
          
          <motion.div style={{ y: y3, opacity }}>
            <AnimatedText
              text="Join our exclusive network of forward-thinking investors and partners who are funding and supporting the next generation of groundbreaking startups."
              className="text-xl text-white/80 mb-10"
              once
            />
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="flex flex-wrap gap-4 justify-center"
            >
              <Button size="lg" className="bg-primary/90 hover:bg-primary text-white backdrop-blur-sm" asChild>
                <Link href="#investor-companies">
                  Explore Our Network
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white/20 text-white hover:bg-white/10 backdrop-blur-sm" asChild>
                <Link href="#contact">
                  Join Our Network
                </Link>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
      
      {/* Scroll indicator */}
      <motion.div 
        className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
        animate={{ 
          y: [0, 10, 0],
          opacity: [0.3, 1, 0.3],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="w-6 h-10 rounded-full border-2 border-white/30 flex items-start justify-center p-1">
          <div className="w-1.5 h-3 bg-white/50 rounded-full" />
        </div>
      </motion.div>
    </div>
  );
}
