'use client';

import { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { cn } from '@/lib/utils';

interface HorizontalScrollProps {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  speed?: number;
}

export function HorizontalScroll({
  children,
  className,
  containerClassName,
  speed = 0.5,
}: HorizontalScrollProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start end', 'end start'],
  });
  
  // Calculate the horizontal scroll based on vertical scroll
  const x = useTransform(scrollYProgress, [0, 1], ['0%', `-${speed * 100}%`]);
  
  return (
    <div 
      ref={containerRef}
      className={cn('relative overflow-hidden', containerClassName)}
    >
      <motion.div
        ref={scrollRef}
        style={{ x }}
        className={cn('flex', className)}
      >
        {children}
      </motion.div>
    </div>
  );
}

interface HorizontalScrollItemProps {
  children: React.ReactNode;
  className?: string;
  width?: string;
}

export function HorizontalScrollItem({
  children,
  className,
  width = 'w-80',
}: HorizontalScrollItemProps) {
  return (
    <div className={cn('flex-shrink-0', width, className)}>
      {children}
    </div>
  );
}
