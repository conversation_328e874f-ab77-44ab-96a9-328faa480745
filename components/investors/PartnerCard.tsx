'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, useMotionTemplate, useMotionValue } from 'framer-motion';
import { ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PartnerCardProps {
  name: string;
  logo: string;
  description: string;
  partnershipType: string;
  website: string;
  index?: number;
}

export function PartnerCard({
  name,
  logo,
  description,
  partnershipType,
  website,
  index = 0,
}: PartnerCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  
  // Mouse position for gradient effect
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    mouseX.set(x);
    mouseY.set(y);
  };
  
  // Create a gradient that follows the mouse
  const background = useMotionTemplate`radial-gradient(
    circle at ${mouseX}px ${mouseY}px,
    rgba(var(--primary-rgb), 0.15) 0%,
    rgba(var(--primary-rgb), 0.05) 30%,
    transparent 60%
  )`;

  return (
    <motion.div
      className="relative overflow-hidden rounded-xl border border-white/10 bg-black/40 backdrop-blur-sm"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.7,
        delay: index * 0.1,
        ease: [0.19, 1, 0.22, 1]
      }}
      viewport={{ once: true, margin: "-50px" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseMove={handleMouseMove}
      whileHover={{
        borderColor: "rgba(var(--primary-rgb), 0.3)",
        boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
      }}
    >
      {/* Animated background gradient */}
      <motion.div 
        className="absolute inset-0 opacity-0 transition-opacity duration-300"
        style={{ background }}
        animate={{ opacity: isHovered ? 1 : 0 }}
      />
      
      <div className="p-6 relative z-10">
        <div className="flex items-center gap-4 mb-4">
          {/* Logo with animation */}
          <motion.div 
            className="relative w-16 h-16 rounded-lg overflow-hidden bg-white/5 flex items-center justify-center"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <Image
              src={logo}
              alt={name}
              width={64}
              height={64}
              className="object-contain p-2"
            />
          </motion.div>
          
          <div>
            <h3 className="text-lg font-bold text-white">{name}</h3>
            <span className="text-xs px-2 py-1 rounded-full bg-primary/20 text-primary">
              {partnershipType}
            </span>
          </div>
        </div>
        
        <p className="text-white/70 text-sm mb-4">{description}</p>
        
        <motion.div 
          className="mt-auto pt-2 border-t border-white/10"
          initial={{ opacity: 0.7 }}
          whileHover={{ opacity: 1 }}
        >
          <Link 
            href={website} 
            target="_blank" 
            className="text-primary flex items-center text-sm hover:underline"
          >
            Visit Website
            <ExternalLink className="ml-1 h-3 w-3" />
          </Link>
        </motion.div>
      </div>
    </motion.div>
  );
}
