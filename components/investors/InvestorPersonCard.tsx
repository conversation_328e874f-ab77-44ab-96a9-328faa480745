'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '@/lib/utils';

interface InvestorPersonCardProps {
  name: string;
  role: string;
  company: string;
  image: string;
  bio: string;
  expertise: string[];
  featured?: boolean;
  index?: number;
}

export function InvestorPersonCard({
  name,
  role,
  company,
  image,
  bio,
  expertise,
  featured = false,
  index = 0,
}: InvestorPersonCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  // Mouse position values
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Smooth spring physics for mouse movement
  const springConfig = { damping: 25, stiffness: 300 };
  const smoothMouseX = useSpring(mouseX, springConfig);
  const smoothMouseY = useSpring(mouseY, springConfig);

  // Transform mouse position into rotation values
  const rotateX = useTransform(smoothMouseY, [-100, 100], [5, -5]);
  const rotateY = useTransform(smoothMouseX, [-100, 100], [-5, 5]);

  // Glow position based on mouse
  const glowPosition = {
    x: useTransform(smoothMouseX, [-100, 100], [0, 100]),
    y: useTransform(smoothMouseY, [-100, 100], [0, 100]),
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return;
    
    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    mouseX.set(e.clientX - centerX);
    mouseY.set(e.clientY - centerY);
  };

  return (
    <motion.div
      ref={ref}
      className={cn(
        'relative overflow-hidden rounded-xl cursor-pointer',
        'transform-gpu shadow-lg shadow-black/30 will-change-transform',
        featured ? 'md:col-span-2' : ''
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.7,
        delay: index * 0.1,
        ease: [0.19, 1, 0.22, 1]
      }}
      viewport={{ once: true, margin: "-50px" }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        mouseX.set(0);
        mouseY.set(0);
      }}
      onMouseMove={handleMouseMove}
      style={{
        rotateX,
        rotateY,
        scale: isHovered ? 1.03 : 1,
        transformStyle: "preserve-3d",
        perspective: 1000,
      }}
      whileHover={{
        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.5)",
      }}
    >
      {/* Dark overlay that fades out on hover */}
      <motion.div
        className="absolute inset-0 z-10 bg-black/50"
        initial={{ opacity: 1 }}
        animate={{ opacity: isHovered ? 0 : 1 }}
        transition={{ duration: 0.3 }}
      />

      {/* Colorful glow effect that follows cursor */}
      <motion.div
        className="absolute inset-0 z-0 opacity-0"
        initial={{ opacity: 0 }}
        animate={{
          opacity: isHovered ? 0.6 : 0,
        }}
        transition={{ duration: 0.3 }}
        style={{
          background: `radial-gradient(circle at ${glowPosition.x}% ${glowPosition.y}%, hsl(var(--primary)) 0%, transparent 80%)`,
          filter: 'blur(15px)',
        }}
      />

      {/* Image with grayscale filter that transitions to color on hover */}
      <motion.div
        className="relative w-full h-full will-change-transform"
        animate={{
          scale: isHovered ? 1.05 : 1
        }}
        transition={{
          duration: 0.8,
          ease: [0.19, 1, 0.22, 1]
        }}
      >
        <Image
          src={image}
          alt={name}
          fill
          priority={index < 4}
          loading={index < 4 ? "eager" : "lazy"}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className={cn(
            'object-cover will-change-transform',
            !isHovered ? 'grayscale brightness-60 contrast-110 transition-all duration-700' : 'transition-all duration-500'
          )}
        />
      </motion.div>

      {/* Content overlay */}
      <div className="absolute inset-0 z-20 flex flex-col justify-end p-6 text-white">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <h3 className="text-xl font-bold">{name}</h3>
          <p className="text-white/80 text-sm">{role}</p>
          <p className="text-primary text-sm mb-2">{company}</p>
          
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ 
              height: isHovered ? 'auto' : 0,
              opacity: isHovered ? 1 : 0
            }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <p className="text-white/70 text-sm mb-2">{bio}</p>
            <div className="flex flex-wrap gap-1 mt-2">
              {expertise.map((skill, i) => (
                <span 
                  key={i}
                  className="text-xs px-2 py-0.5 rounded-full bg-white/10 text-white/90"
                >
                  {skill}
                </span>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  );
}
