'use client';

import Link from 'next/link';
import { useLanguage } from '@/lib/context/language-context';
import { Facebook, Instagram, Youtube } from 'lucide-react';
import { motion } from 'framer-motion';
import { GlassCard } from '@/components/ui/aceternity/bokeh-background';
import { TextReveal } from '@/components/ui/aceternity/text-reveal';
import { MovingBorder } from '@/components/ui/aceternity/moving-border';


export default function ProgramsFooter() {
  const { language } = useLanguage();

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  const linkVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.3,
      },
    },
  };

  const socialVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: (i: number) => ({
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20,
        delay: 0.5 + i * 0.1,
      },
    }),
  };

  return (
    <footer className="relative border-t border-primary/20">

      {/* Main content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-5 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Column 1: About Us */}
          <motion.div variants={itemVariants}>
            <TextReveal>
              <h3 className="font-semibold text-lg mb-4 text-primary">
                {language === 'mn' ? 'БИДНИЙ ТУХАЙ' : 'ABOUT US'}
              </h3>
            </TextReveal>
            <motion.ul className="space-y-3" variants={containerVariants}>
              <motion.li variants={linkVariants}>
                <Link href="/about#whatWeDo" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Бид юу хийдэг вэ?' : 'What We Do'}
                </Link>
              </motion.li>
              <motion.li variants={linkVariants}>
                <Link href="/about#ourTeam" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Манай баг' : 'Our Team'}
                </Link>
              </motion.li>
              <motion.li variants={linkVariants}>
                <Link href="/about#mentors" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Менторууд' : 'Mentors'}
                </Link>
              </motion.li>
            </motion.ul>
          </motion.div>

          {/* Column 2: Programs */}
          <motion.div variants={itemVariants}>
            <TextReveal>
              <h3 className="font-semibold text-lg mb-4 text-primary">
                {language === 'mn' ? 'ХӨТӨЛБӨР' : 'PROGRAMS'}
              </h3>
            </TextReveal>
            <motion.ul className="space-y-3" variants={containerVariants}>
              <motion.li variants={linkVariants}>
                <Link href="/programs" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Хурдасгуур хөтөлбөр' : 'Accelerator Program'}
                </Link>
              </motion.li>
              <motion.li variants={linkVariants}>
                <Link href="/programs" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Гэрлийн марафон' : 'Light Marathon'}
                </Link>
              </motion.li>
              <motion.li variants={linkVariants}>
                <Link href="/programs" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Ирээдүй хөтөлбөр' : 'Future Program'}
                </Link>
              </motion.li>
              <motion.li variants={linkVariants}>
                <Link href="/programs" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Подкаст' : 'Podcast'}
                </Link>
              </motion.li>
            </motion.ul>
          </motion.div>

          {/* Column 3: Ecosystem */}
          <motion.div variants={itemVariants}>
            <TextReveal>
              <h3 className="font-semibold text-lg mb-4 text-primary">
                {language === 'mn' ? 'ЭКОСИСТЕМ ОРОЛЦОГЧИД' : 'ECOSYSTEM PARTICIPANTS'}
              </h3>
            </TextReveal>
            <motion.ul className="space-y-3" variants={containerVariants}>
              <motion.li variants={linkVariants}>
                <Link href="/investors" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Хөрөнгө оруулагчид' : 'Investors'}
                </Link>
              </motion.li>
              <motion.li variants={linkVariants}>
                <Link href="/partners" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Хамтрагчид' : 'Partners'}
                </Link>
              </motion.li>
            </motion.ul>
          </motion.div>

          {/* Column 4: Support */}
          <motion.div variants={itemVariants}>
            <TextReveal>
              <h3 className="font-semibold text-lg mb-4 text-primary">
                {language === 'mn' ? 'ДЭМЖЛЭГ МЭДЭЭЛЭЛ' : 'SUPPORT INFORMATION'}
              </h3>
            </TextReveal>
            <motion.ul className="space-y-3" variants={containerVariants}>
              <motion.li variants={linkVariants}>
                <Link href="/news" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Мэдээ' : 'News'}
                </Link>
              </motion.li>
              <motion.li variants={linkVariants}>
                <Link href="/events" className="text-white/80 hover:text-white transition-colors inline-flex items-center">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2"></span>
                  {language === 'mn' ? 'Арга хэмжээ' : 'Events'}
                </Link>
              </motion.li>
            </motion.ul>
          </motion.div>

          {/* Column 5: Contact */}
          <motion.div variants={itemVariants}>
            <TextReveal>
              <h3 className="font-semibold text-lg mb-4 text-primary">
                {language === 'mn' ? 'ХОЛБОО БАРИХ' : 'CONTACT US'}
              </h3>
            </TextReveal>
            <GlassCard className="p-4 mb-4 backdrop-blur-sm">
              <motion.ul className="space-y-3" variants={containerVariants}>
                <motion.li variants={linkVariants} className="flex items-start">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2 mt-2"></span>
                  <p className="text-white/90">
                    {language === 'mn'
                      ? 'Хаяг: ХУД, 11-р хороо, Рома Таун, 40/3 байр, 101 тоот'
                      : 'Address: Khan-Uul district, 11th khoroo, Roma Town, Building 40/3, Suite 101'}
                  </p>
                </motion.li>
                <motion.li variants={linkVariants} className="flex items-start">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2 mt-2"></span>
                  <p className="text-white/90">
                    {language === 'mn' ? 'Утас: +976 77995566' : 'Phone: +976 77995566'}
                  </p>
                </motion.li>
                <motion.li variants={linkVariants} className="flex items-start">
                  <span className="w-1.5 h-1.5 rounded-full bg-primary/70 mr-2 mt-2"></span>
                  <p className="text-white/90">
                    {language === 'mn' ? 'Имэйл: <EMAIL>' : 'Email: <EMAIL>'}
                  </p>
                </motion.li>
              </motion.ul>
            </GlassCard>

            {/* Social Media Icons */}
            <div className="mt-6 flex space-x-4">
              {[
                { icon: <Facebook className="h-4 w-4 text-white" />, href: "https://facebook.com", label: "Facebook", custom: 0 },
                { icon: <Instagram className="h-4 w-4 text-white" />, href: "https://instagram.com", label: "Instagram", custom: 1 },
                { icon: <Youtube className="h-4 w-4 text-white" />, href: "https://youtube.com", label: "YouTube", custom: 2 }
              ].map((social) => (
                <motion.div
                  key={social.label}
                  custom={social.custom}
                  variants={socialVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                >
                  <MovingBorder
                    containerClassName="rounded-full"
                    duration={3000}
                    rx="50%"
                    ry="50%"
                    borderClassName="border-primary/50"
                  >
                    <a
                      href={social.href}
                      className="w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm flex items-center justify-center hover:bg-primary/20 transition-colors duration-300 border border-primary/30"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <span className="sr-only">{social.label}</span>
                      {social.icon}
                    </a>
                  </MovingBorder>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>



        {/* Copyright */}
        <motion.div
          className="mt-16 pt-8 border-t border-primary/20 flex flex-col md:flex-row justify-between items-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="text-white/60 text-sm">
            © BEL INVESTMENT LLC 2025
          </p>

          <div className="flex space-x-4 mt-4 md:mt-0">
            {[
              { href: "/privacy", label: language === 'mn' ? "Нууцлалын бодлого" : "Privacy Policy" },
              { href: "/terms", label: language === 'mn' ? "Үйлчилгээний нөхцөл" : "Terms of Service" }
            ].map((link, i) => (
              <motion.div
                key={link.href}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.9 + (i * 0.1) }}
                viewport={{ once: true }}
              >
                <Link
                  href={link.href}
                  className="text-white/60 text-sm hover:text-white/90 transition-colors"
                >
                  {link.label}
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
