'use client';

import React from 'react';
import ProgramsFooter from '@/components/programs/ProgramsFooter';
import Footer from '@/components/shared/Footer';
import { usePathname } from 'next/navigation';

export default function ProgramsFooterWrapper() {
  const pathname = usePathname();
  const isProgramsMainPage = pathname === '/programs';

  return (
    <>
      {isProgramsMainPage ? <ProgramsFooter /> : <Footer />}
    </>
  );
}
