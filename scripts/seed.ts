import { seedCourses } from '../lib/services/courseService';
import { prisma } from '../lib/prisma';

async function main() {
  console.log('Starting database seed...');
  
  try {
    await seedCourses();
    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
